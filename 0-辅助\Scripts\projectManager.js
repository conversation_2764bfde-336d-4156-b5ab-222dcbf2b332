/**
 * 项目管理脚本 - 简化版
 */

module.exports = async (tp) => {
  // 配置映射
  const configs = {
    每周计划: {
      template: "TP-Project-Plan",
      folder: "1-每周计划",
      prefix: "Plan",
    },
    每日执行: {
      template: "TP-Project-Do",
      folder: "2-每日执行",
      prefix: "Do",
    },
    每周评审: {
      template: "TP-Project-Review",
      folder: "3-每周评审",
      prefix: "Review",
    },
    每周回顾: {
      template: "TP-Project-Retro",
      folder: "4-每周回顾",
      prefix: "Retro",
    },
  };

  try {
    // 选择操作类型
    const action = await tp.system.suggester(
      [
        "【0】+新建项目",
        "【1】每周计划",
        "【2】每日执行",
        "【3】每周评审",
        "【4】每周回顾",
      ],
      ["创建新项目", "每周计划", "每日执行", "每周评审", "每周回顾"]
    );

    if (!action) {
      // 用户取消选择，直接退出
      return;
    }

    if (action === "创建新项目") {
      await createNewProject();
    } else {
      await createProjectFile(action);
    }
  } catch (error) {
    new Notice(`操作失败: ${error.message}`);
  }

  // 确保脚本不返回任何值
  return;

  // 创建新项目
  async function createNewProject() {
    const name = await tp.system.prompt("请输入项目名称:");
    if (!name?.trim()) {
      new Notice("项目名称不能为空");
      return;
    }

    const projectPath = `2-项目/${name.trim()}`;
    if (app.vault.getAbstractFileByPath(projectPath)) {
      return;
    }

    await app.vault.createFolder(projectPath);
    const file = await createFile(
      `${projectPath}/${name.trim()}-首页.md`,
      "TP-Project-Home"
    );
    if (file) {
      await app.workspace.getLeaf("tab").openFile(file);
    }
  }

  // 创建项目文件
  async function createProjectFile(fileType) {
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
      new Notice("请先打开一个项目文件");
      return;
    }

    const pathParts = activeFile.path.split("/");
    if (pathParts[0] !== "2-项目") {
      new Notice("请在项目文件夹中执行此操作");
      return;
    }

    const projectName = pathParts[1];
    const config = configs[fileType];

    // 生成文件名
    const now = new Date();
    const fileName =
      fileType === "每日执行"
        ? `${config.prefix}-${now.toISOString().split("T")[0]}.md`
        : `${config.prefix}-${now.getFullYear()}-WK${getWeekNumber(now)
            .toString()
            .padStart(2, "0")}.md`;

    const folderPath = `2-项目/${projectName}/${config.folder}`;
    const filePath = `${folderPath}/${fileName}`;

    // 确保文件夹存在
    if (!app.vault.getAbstractFileByPath(folderPath)) {
      await app.vault.createFolder(folderPath);
    }

    // 检查文件是否已存在
    const existingFile = app.vault.getAbstractFileByPath(filePath);
    if (existingFile) {
      await app.workspace.getLeaf("tab").openFile(existingFile);
      return;
    }

    // 创建新文件
    const file = await createFile(filePath, config.template);
    if (file) {
      await app.workspace.getLeaf("tab").openFile(file);
    }
  }

  // 根据模板创建文件
  async function createFile(filePath, templateName) {
    try {
      const templatePath = `0-辅助/Templater/Notes/${templateName}.md`;
      const templateFile = app.vault.getAbstractFileByPath(templatePath);

      if (!templateFile) {
        new Notice(`模板文件不存在: ${templatePath}`);
        return false;
      }

      let content = await app.vault.read(templateFile);
      const file = await app.vault.create(filePath, content);

      if (file) {
        // 获取项目名
        const pathParts = filePath.split("/");
        const projectName = pathParts[1];

        // 后处理：根据模板类型进行内容替换
        let modifiedContent = content;

        if (templateName === "TP-Project-Plan") {
          // 每周计划：替换改进事项为指向当前项目的改进待办事项文件
          modifiedContent = content.replace(
            /> > \[!todo\] 改进事项/g,
            "> > [!todo] [[改进待办事项|改进事项]]"
          );
        } else if (templateName === "TP-Project-Review") {
          // 每周评审：替换关联KR为指向当前项目首页的OKR设定章节
          // 使用 Obsidian 的 Wikilink 语法，但在表格环境中需要特殊处理
          // 尝试使用 [[页面#章节|显示文本]] 的标准格式
          modifiedContent = content.replace(
            /关联KR/g,
            `[[${projectName}-首页#1. OKR设定|关联KR]]`
          );
        }

        // 如果有修改，更新文件内容
        if (modifiedContent !== content) {
          await app.vault.modify(file, modifiedContent);
        }
      }

      return file;
    } catch (error) {
      new Notice(`创建文件失败: ${error.message}`);
      return false;
    }
  }

  // 获取ISO周数
  function getWeekNumber(date) {
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    d.setDate(d.getDate() + 3 - ((d.getDay() + 6) % 7));
    const week1 = new Date(d.getFullYear(), 0, 4);
    return (
      1 +
      Math.round(((d - week1) / 86400000 - 3 + ((week1.getDay() + 6) % 7)) / 7)
    );
  }
};
