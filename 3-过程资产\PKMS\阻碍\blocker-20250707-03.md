---
aliases:
  - 阻碍判断依据缺失
created_Date: 2025-07-07
status: 进行中
relation:
  - "[[blocker-20250606-01]]"
cssclasses:
  - c3
type: 阻塞型
---
# 1. 基础信息

- 背景描述：
	- 项目执行期间，遇到问题或者困扰时
- 可观测现象：
	- 被记录的阻碍好像不是“阻碍”（可能是闪念或者优化改善项）
- 触发条件：
	- 当前困扰影响了工作流或者影响了当前工作推进
- 影响：
	- “阻碍”记录不科学或错误记录，导致问题被复杂化，不利于后续解决和复盘

# 2. 临时方案

| 生效时间                | 目标               | 临时方案描述                                                                                                                                                                     | 决策依据                        | 已知风险与局限 | 状态跟踪 | 债务等级 | 知识缺口 |
| ------------------- | ---------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------- | ------- | ---- | ---- | ---- |
| 2025-07-08 10:03:35 | 保证影响当前工作的问题被全部记录 | 1、IF“遇到问题”&“影响当前工作推进”，“阻碍”；<br>2、否则，“闪念“；                                                                                                                                  | “影响当前工作推进”的问题能够体现阻碍的本质      | 判断条件模糊  | 已失效  |      |      |
| 2025-07-10 17:58:20 | 细化阻碍判断的条件        | 1、IF“遇到问题”&“降低了向目标前进的速度或效率”，“阻碍”；<br>2、IF “产生疑问”&“为实现短期目标而牺牲长期质量或可维护性的妥协决策或疏漏”，“技术债”；<br>3、否则，“闪念”或“洞见/发现”                                                                 |                             |         | 已失效  |      |      |
| 2025-08-04 16:07:50 | 阻碍的系统化判断         | 1、若「完全阻塞工作流」或「威胁价值交付的有效性」，则应记录为阻碍；<br>（1）有效性有效性体现在「正确性：成果符合用户真实需求」、「及时性：在价值窗口期内交付」、「可持续性：不埋藏未来崩溃的隐患」）<br>2、若「部分阻碍工作流」，应记录为“优化项”，然后再有选择的纳入常规迭代计划；<br>3、若不涉及影响价值交付的有效性，则无需记录 | 敏捷的终极目标不是“完成任务”，而是交付可用的商业价值 |         | 生效中  |      |      |
# 3. 根因分析

- 核心问题是什么？ --> 

# 5. 最终方案（可选）

| 生效时间 | 根本原因 | 方案描述 | 决策依据 | 已知风险与局限 |
| ---- | ---- | ---- | ---- | ------- |
|      |      |      |      |         |
