async function weeklyKrProgress({ dv }) {
  // 获取当前系统时间的ISO周数和年份
  function getISOWeek(date) {
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    d.setDate(d.getDate() + 4 - (d.getDay() || 7));
    const yearStart = new Date(d.getFullYear(), 0, 1);
    const weekNo = Math.ceil(((d - yearStart) / 86400000 + 1) / 7);
    return [d.getFullYear(), weekNo];
  }

  // 标准化单元格内容
  function normalizeCellContent(cell) {
    return cell
      .trim()
      .replace(/<br>/gi, "\n")
      .replace(/\s*\n\s*/g, "\n")
      .trim();
  }

  // 调整数据行列数以匹配表头
  function adjustRowColumns(row, headerLength) {
    // 补齐缺失的列
    while (row.length < headerLength) {
      row.push("");
    }

    // 处理多余的列
    if (row.length > headerLength) {
      if (row.length - headerLength <= 2) {
        // 少量多余列，合并到最后一个有效列
        const extraData = row.slice(headerLength - 1).join(" ");
        row = row.slice(0, headerLength - 1);
        row[headerLength - 2] += " " + extraData;
      } else {
        // 过多多余列，直接截断
        row = row.slice(0, headerLength);
      }
    }

    return row;
  }

  // 简化的表头解析函数
  function parseTableHeader(headerLine) {
    const fullLine = "|" + headerLine + "|";
    const headerRow = [];
    let currentCell = "";
    let bracketDepth = 0;

    for (let i = 0; i < fullLine.length; i++) {
      const char = fullLine[i];

      if (char === "[") {
        bracketDepth++;
        currentCell += char;
      } else if (char === "]") {
        bracketDepth--;
        currentCell += char;
      } else if (char === "|" && bracketDepth === 0) {
        if (currentCell.trim()) {
          headerRow.push(currentCell.trim());
        }
        currentCell = "";
      } else {
        currentCell += char;
      }
    }

    if (currentCell.trim()) {
      headerRow.push(currentCell.trim());
    }

    return headerRow;
  }

  // 表格解析函数
  function parseMarkdownTables(markdown) {
    const tables = [];
    const tableRegex = /\|(.+)\|\s*\n\s*\|(.+)\|\s*\n((?:\|.+\|\s*\n?)*)/g;

    let tableMatch;
    while ((tableMatch = tableRegex.exec(markdown)) !== null) {
      try {
        const headerRow = parseTableHeader(tableMatch[1]);

        if (headerRow.length === 0) {
          continue;
        }

        // 处理数据行
        const dataRows = tableMatch[3]
          .split("\n")
          .filter((row) => {
            const trimmed = row.trim();
            return (
              trimmed !== "" &&
              trimmed.includes("|") &&
              !trimmed.startsWith("|--")
            );
          })
          .map((row) => {
            const parts = row.split("|");
            const cells = parts.slice(1, -1);
            const normalizedCells = cells.map((cell) =>
              normalizeCellContent(cell)
            );
            return adjustRowColumns(normalizedCells, headerRow.length);
          })
          .filter((row) => {
            return row.some(
              (cell) => cell !== "" && cell !== "-" && cell !== "~"
            );
          });

        if (dataRows.length > 0) {
          tables.push({ header: headerRow, data: dataRows });
        }
      } catch (e) {
        console.warn("表格解析错误:", e);
      }
    }
    return tables;
  }

  const [targetYear, targetWeek] = getISOWeek(new Date());
  const projectName = dv.current().file.path.split("/")[1].trim();
  const targetFolder = `2-项目/${projectName}/3-每周评审`;
  const tableTitlePattern = "KR进度";

  let allTableData = [];

  // 第一步：快速筛选符合条件的文件
  const candidateFiles = [];
  for (let file of dv.pages(`"${targetFolder}"`).file) {
    const fileMatch = file.name.match(/^Review-(\d{4})-WK(\d{2})$/);
    if (!fileMatch) continue;

    const fileYear = parseInt(fileMatch[1]);
    const fileWeek = parseInt(fileMatch[2]);

    if (
      fileYear < targetYear ||
      (fileYear === targetYear && fileWeek < targetWeek)
    ) {
      candidateFiles.push({
        file,
        year: fileYear,
        week: fileWeek,
      });
    }
  }

  // 第二步：按时间倒序排序（从最新到最早）
  candidateFiles.sort((a, b) => {
    if (a.year !== b.year) return b.year - a.year;
    return b.week - a.week;
  });

  // 第三步：仅处理最近4周的文件，找到第一个有效文件即停止
  let foundFile = null;
  let foundTables = [];

  for (let i = 0; i < Math.min(4, candidateFiles.length); i++) {
    const candidate = candidateFiles[i];

    // 加载文件内容
    const content = await dv.io.load(candidate.file.path);

    // 识别指定标题下的内容区域
    const headingRegex = new RegExp(
      `(?:^|\\n)#+\\s*.*${tableTitlePattern}.*[^\\n]*\\n([\\s\\S]*?)(?=\\n#|$)`,
      "i"
    );

    const match = content.match(headingRegex);
    if (!match || !match[1]) continue;

    const sectionContent = match[1].trim();

    // 解析表格
    const tables = parseMarkdownTables(sectionContent);

    // 找到有效文件，记录并跳出循环
    if (tables.length > 0) {
      foundFile = candidate;
      foundTables = tables;
      break;
    }
  }

  // 处理找到的文件
  if (foundFile && foundTables.length > 0) {
    // 从 foundFile 中获取 year 和 week
    const { year, week } = foundFile;
    // 创建文件链接
    const weekTag = `${year}-WK${week.toString().padStart(2, "0")}`;
    const fileName = `Review-${weekTag}.md`;
    const fileLink = dv.fileLink(
      `${targetFolder}/${fileName}`,
      false,
      fileName
    );

    // 处理每个表格
    for (const table of foundTables) {
      const { header, data } = table;

      if (allTableData.length === 0) {
        allTableData.push([...header, "回顾链接"]);
      }

      data.forEach((row) => {
        allTableData.push([...row, fileLink]);
      });
    }
  }

  // 按Blocker ID排序
  if (allTableData.length > 1) {
    const headers = allTableData[0];
    const dataRows = allTableData.slice(1);

    const blockerIdColumnIndex = headers.indexOf("来源");

    dataRows.sort((a, b) => {
      const blockerIdA = a[blockerIdColumnIndex] || "";
      const blockerIdB = b[blockerIdColumnIndex] || "";

      const dateMatchA = blockerIdA.match(/blocker-(\d{8})/);
      const dateMatchB = blockerIdB.match(/blocker-(\d{8})/);

      if (dateMatchA && dateMatchB) {
        return dateMatchB[1].localeCompare(dateMatchA[1]);
      } else if (dateMatchA) {
        return -1;
      } else if (dateMatchB) {
        return 1;
      }
      return blockerIdA.localeCompare(blockerIdB);
    });

    allTableData = [headers, ...dataRows];
  }

  // 输出结果
  if (allTableData.length > 0) {
    dv.table(allTableData[0], allTableData.slice(1));
  } else {
    dv.el("p", "📊 未找到近期的KR进度数据");
  }
}

weeklyKrProgress(input);
