---
createdDate: 2025-08-09
aliases:
  - 技术债模板“元数据”字段、“基础信息”设计不合理
location:
  - "[[TP-Project-TechDebt]]"
type: 系统债
priority: P0
status: 已关闭
relatedDebt:
  - "[[td-20250826-01]]"
  - "[[td-20250827-01]]"
cssclasses:
  - c3
---
	# 1. 基础信息

| 序号  | 类型   | 场景描述       | 可观测现象                                       | 关键影响                        | 触发条件（如何发现？）                                                                  | 初步分析/根本原因        |
| --- | ---- | ---------- | ------------------------------------------- | --------------------------- | ---------------------------------------------------------------------------- | ---------------- |
| 1   | 原始债务 | 根据模板记录技术债时 | 1、很难快速完成元数据字段的评估<br>2、基础信息填写顺序违背直觉，思维出现多次断裂 | 技术债记录中断，后续迭代无法正常开展（有计划偿还债务） | 1、判断技术债类型（`阻碍型/成本型/战略型/无害型`）、优先级（`高/中/低`）<br>2、记录基础信息（`发现场景、发现位置、根本原因、关键影响`） | 对敏捷项目开发技术债登记缺乏实践 |
# 2. 应急方案
| 序号  | 日期        | 行动                        | 退出条件     |
| --- | --------- | ------------------------- | -------- |
| 1   | 2025-8-09 | 停止记录技术债详细信息，仅简单填写`发生创景`信息 | 模板优化方案上线 |
# 3. 偿还计划

- [x] 重新设计模板`type`、`priority`字段 ➕ 2025-08-12 ⏳ 2025-08-11 ✅ 2025-08-13
- [x] 利用[[td-20250809-01]]测试模板`type`、`priority`字段的合理性➕ 2025-08-12 ⏳ 2025-08-11 ✅ 2025-08-13
- [x] 测试模板“基础信息”部分的逻辑合理性（[[td-20250809-01]]） ➕ 2025-08-13 ⏳ 2025-08-11 ✅ 2025-08-13
- [x] 重新设计模板`position`、`stayus`字段 ➕ 2025-08-13 ⏳ 2025-08-14 ✅ 2025-08-13
- [x] 探索技术债登记的核心元素 ➕ 2025-08-14 ⏳ 2025-08-14 ✅ 2025-08-27
- [x] 分析技术债的产生、识别、方案评估、登记、优先级评估、计划与执行、反馈的逻辑，并创建[[技术债处理流程图.svg]] ➕ 2025-08-14 ⏳ 2025-08-14 ✅ 2025-08-27
- [x] 重新设计模板正文部分 ➕ 2025-08-12 ⏳ 2025-08-11 ✅ 2025-08-13
- [x] 优化「[[TP-Project-TechDebt]]」模板正文内容 ➕ 2025-08-14 ⏳ 2025-08-14 ✅ 2025-08-26
- [ ] 

# 4. 验收清单

- [x] 模板每个元数据字段评估时间≤3s ✅ 2025-08-27
- [x] 正文模块内容符合直觉流，且未频繁出现思维断裂 ✅ 2025-08-27
- [x] 流程图逻辑合理、内容无缺失 ✅ 2025-08-27