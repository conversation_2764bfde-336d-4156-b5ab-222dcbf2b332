{"presets": {"project_improvements": "limit 5\n  heading includes 行动\n  description regex matches /\\S/\n  hide backlink\n  is not blocked\n  sort by priority\n  filter by function \\\n  const projectName = query.file.path.split(\"/\")[1]; \\\n  const targetPath = `2-项目/${projectName}/改进待办事项`; \\\n  const pathMatches = task.file.path.includes(targetPath);\\\n  return pathMatches;", "project_tasks_todo": "hide backlink\nhide created date\nhide scheduled date\nfilter by function \\\nconst fileName = query.file.filenameWithoutExtension;\\\nconst dateMatch = fileName.match(/\\d{4}-\\d{2}-\\d{2}/);\\\nif (!dateMatch) return false;\\\nconst targetDate = moment(dateMatch[0]);\\\nconst year = targetDate.isoWeekYear();\\\nconst weekNumber = targetDate.isoWeek().toString().padStart(2, '0');\\\nconst targetPath = `Plan-${year}-WK${weekNumber}`;\\\nconst pathMatches = task.file.path.includes(targetPath);\\\nconst titleKeywords = \"任务\";\\\nconst headingMatches = task.heading && task.heading.includes(titleKeywords);\\\nconst dateMatches = task.scheduled?.moment?.isSame(targetDate, 'day');\\\nconst isWeekPlanTask = pathMatches && headingMatches && dateMatches;\\\nconst currentPath = query.file.path;\\\nconst projectName = currentPath ? currentPath.split(\"/\")[1] : \"\";\\\nconst techDebtPath = `3-过程资产/${projectName}/技术债`;\\\nconst isTechDebtTask = projectName && \\\ntask.file && task.file.path && \\\ntask.file.path.includes(techDebtPath) && dateMatches && \\\ntask.description?.trim().length > 0;\\\nreturn Boolean(isWeekPlanTask) || Boolean(isTechDebtTask);", "project_tasks_unplanned": "not done\nno scheduled date\nhide backlink\nfilter by function \\\nconst fileName = query.file.filenameWithoutExtension;\\\nconst dateMatch = fileName.match(/\\d{4}-\\d{2}-\\d{2}/);\\\nif (!dateMatch) return false;\\\nconst targetDate = moment(dateMatch[0]);\\\nconst year = targetDate.isoWeekYear();\\\nconst weekNumber = targetDate.isoWeek().toString().padStart(2, '0');\\\nconst targetPath = `Plan-${year}-WK${weekNumber}`;\\\nconst pathMatches = task.file.path.includes(targetPath);\\\nconst titleKeywords = \"任务\";\\\nconst headingMatches = task.heading && task.heading.includes(titleKeywords);\\\nconst hasDescription = task.description?.trim().length > 0;\\\nconst isWeekPlanTask = pathMatches && headingMatches && hasDescription;\\\nconst currentPath = query.file.path;\\\nconst projectName = currentPath ? currentPath.split(\"/\")[1] : \"\";\\\nconst techDebtPath = `3-过程资产/${projectName}/技术债`;\\\nconst isTechDebtTask = projectName && task.file && task.file.path && \\\ntask.file.path.includes(techDebtPath) && \\\ntask.created && \\\ntask.created.moment && \\\ntask.created.moment.isoWeek().toString().padStart(2, '0') === weekNumber && \\\ntask.description?.trim().length > 0;\\\nreturn Boolean(isWeekPlanTask) || Boolean(isTechDebtTask);"}, "globalQuery": "", "globalFilter": "", "removeGlobalFilter": false, "taskFormat": "tasksPluginEmoji", "setCreatedDate": false, "setDoneDate": true, "setCancelledDate": false, "autoSuggestInEditor": false, "autoSuggestMinMatch": 1, "autoSuggestMaxItems": 20, "provideAccessKeys": true, "useFilenameAsScheduledDate": false, "filenameAsScheduledDateFormat": "", "filenameAsDateFolders": [], "recurrenceOnNextLine": true, "removeScheduledDateOnRecurrence": false, "statusSettings": {"coreStatuses": [{"symbol": " ", "name": "Todo", "nextStatusSymbol": "x", "availableAsCommand": true, "type": "TODO"}, {"symbol": "x", "name": "Done", "nextStatusSymbol": " ", "availableAsCommand": true, "type": "DONE"}], "customStatuses": [{"symbol": "-", "name": "Cancelled", "nextStatusSymbol": " ", "availableAsCommand": true, "type": "CANCELLED"}]}, "features": {"INTERNAL_TESTING_ENABLED_BY_DEFAULT": true}, "generalSettings": {}, "headingOpened": {"Core Statuses": true, "Custom Statuses": true, "核心状态": true, "自定义状态": true}, "debugSettings": {"ignoreSortInstructions": false, "showTaskHiddenData": false, "recordTimings": false}, "loggingOptions": {"minLevels": {"": "info", "tasks": "info", "tasks.Cache": "info", "tasks.Events": "info", "tasks.File": "info", "tasks.Query": "info", "tasks.Task": "info"}}}