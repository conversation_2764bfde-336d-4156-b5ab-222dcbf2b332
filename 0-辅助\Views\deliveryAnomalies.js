async function deliveryAnomalies({ dv }) {
	// 获取当前系统时间的ISO周数和年份
	function getISOWeek(date) {
		const d = new Date(date);
		d.setHours(0, 0, 0, 0);
		d.setDate(d.getDate() + 4 - (d.getDay() || 7));
		const yearStart = new Date(d.getFullYear(), 0, 1);
		const weekNo = Math.ceil(((d - yearStart) / 86400000 + 1) / 7);
		return [d.getFullYear(), weekNo];
	}

	const [targetYear, targetWeek] = getISOWeek(new Date());
	const projectName = dv.current().file.path.split("/")[1].trim();
	const targetFolder = `2-项目/${projectName}/3-每周评审`;
	const tableTitlePattern = "交付异常"

	let allTableData = [];

	// 第一步：快速筛选符合条件的文件
	const candidateFiles = [];
	for (let file of dv.pages(`"${targetFolder}"`).file) {
		const fileMatch = file.name.match(/^Review-(\d{4})-WK(\d{2})$/);
		if (!fileMatch) continue;
		
		const fileYear = parseInt(fileMatch[1]);
		const fileWeek = parseInt(fileMatch[2]);

		if (fileYear < targetYear || (fileYear === targetYear && fileWeek < targetWeek)) {
			candidateFiles.push({
				file,
				year: fileYear,
				week: fileWeek,
				path: file.path
			});
		}
	}

	// 第二步：按时间倒序排序（从最新到最早）
	candidateFiles.sort((a, b) => {
		if (a.year !== b.year) return b.year - a.year;
		return b.week - a.week;
	});

	// 第三步：仅处理最近4周的文件，找到第一个有效文件即停止
	let foundFile = null;
	let foundTables = [];
	
	for (let i = 0; i < Math.min(4, candidateFiles.length); i++) {
		const candidate = candidateFiles[i];
		const { file, year, week, path } = candidate;
		
		// 加载文件内容
		const content = await dv.io.load(path);
		
		// 识别指定标题下的内容区域
		const headingRegex = new RegExp(
			`(?:^|\\n)#+\\s*.*${tableTitlePattern}.*[^\\n]*\\n([\\s\\S]*?)(?=\\n#|$)`,
			"i"
		);
		
		const match = content.match(headingRegex);
		if (!match || !match[1]) continue;
		
		const sectionContent = match[1].trim();
		
		// 表格解析函数 - 增强版，确保表格结构完整性
		const parseMarkdownTables = (markdown) => {
			const tables = [];
			const tableRegex = /\|([^\n]+\|)\s*\n\s*\|(\s*:?[-~]+:?\s*\|)+\s*\n((?:\s*\|[^\n]+\|[^\S\r?\n]*\n?)+)/g;
			
			let tableMatch;
			while ((tableMatch = tableRegex.exec(markdown)) !== null) {
				try {
					const headerRow = tableMatch[1].split('|')
						.map(cell => cell.trim())
						.filter(cell => cell !== '');
					
					// 验证表头有效性 - 必须包含有效列
					if (headerRow.length === 0) {
						continue;
					}
					
					// 处理数据行 - 增强验证
					const dataRows = tableMatch[3].split('\n')
						.filter(row => {
							const trimmed = row.trim();
							return trimmed !== '' &&
								   trimmed.includes('|') &&
								   !trimmed.startsWith('|--') &&
								   trimmed !== tableMatch[1].trim(); // 避免与表头重复
						})
						.map(row => {
							const cells = row.split('|')
								.slice(1, -1)
								.map(cell => {
									const normalized = cell.trim()
										.replace(/<br>/gi, '\n')
										.replace(/\s*\n\s*/g, '\n')
										.trim();
									return normalized;
								});
							return cells;
						})
						.filter(row => {
							// 更严格的过滤：行内至少有一个非空单元格
							return row.some(cell => cell !== '' && cell !== '-' && cell !== '~');
						});
					
					// 增强验证：必须同时满足数据行存在且结构有效
					if (dataRows.length > 0 && headerRow.length > 0) {
						// 额外验证：确保数据行与表头列数基本匹配（允许轻微差异）
						const validDataRows = dataRows.filter(row =>
							row.length > 0 &&
							Math.abs(row.length - headerRow.length) <= 2
						);
						
						if (validDataRows.length > 0) {
							tables.push({ header: headerRow, data: validDataRows });
						}
					}
				} catch (e) {
					console.warn("表格解析错误:", e);
				}
			}
			return tables;
		};
		
		// 解析表格
		const tables = parseMarkdownTables(sectionContent);
		
		// 找到有效文件，记录并跳出循环
		if (tables.length > 0) {
			foundFile = candidate;
			foundTables = tables;
			break;
		}
	}
	
	// 处理找到的文件
	if (foundFile && foundTables.length > 0) {
		const { year, week } = foundFile;
		// 创建文件链接
		const weekTag = `${year}-WK${week.toString().padStart(2, '0')}`;
		const fileName = `Review-${weekTag}.md`;
		const fileLink = dv.fileLink(`${targetFolder}/${fileName}`, false, fileName);
		
		// 处理每个表格
		for (const table of foundTables) {
			const { header, data } = table;
			
			if (allTableData.length === 0) {
				allTableData.push([...header, "回顾链接"]);
			}
			
			data.forEach(row => {
				allTableData.push([...row, fileLink]);
			});
		}
	}

	// 按Blocker ID排序
	if (allTableData.length > 1) {
		const headers = allTableData[0];
		const dataRows = allTableData.slice(1);
		
		const blockerIdColumnIndex = headers.indexOf("来源");
		
		dataRows.sort((a, b) => {
			const blockerIdA = a[blockerIdColumnIndex] || "";
			const blockerIdB = b[blockerIdColumnIndex] || "";
			
			const dateMatchA = blockerIdA.match(/blocker-(\d{8})/);
			const dateMatchB = blockerIdB.match(/blocker-(\d{8})/);
			
			if (dateMatchA && dateMatchB) {
				return dateMatchB[1].localeCompare(dateMatchA[1]);
			}
			else if (dateMatchA) {
				return -1;
			}
			else if (dateMatchB) {
				return 1;
			}
			return blockerIdA.localeCompare(blockerIdB);
		});
		
		allTableData = [headers, ...dataRows];
	}

	// 输出结果
	if (allTableData.length > 0) {
		dv.table(allTableData[0], allTableData.slice(1));
	} else {
		dv.el("p", "🎉 无历史交付异常数据");
	}
}

deliveryAnomalies(input);