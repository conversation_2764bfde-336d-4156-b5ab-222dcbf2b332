# 1. 周目标

- 优化「每周计划」内容布局（“改进事项”与“技术债”）
- 修复「每周评审」统计看板异常（“空任务”、“成果内容重复“、”技术债任务未覆盖“）
- 补全项目核心组件说明指南
- 简化项目组件查询代码（利用`dv.view()`和task代码片段功能进行优化，排除不确定查询模块）

# 2. 验收标准

| 序号  | 交付物                         | 标准/通过条件                                                                       | 备注  |
| --- | --------------------------- | ----------------------------------------------------------------------------- | --- |
| 1   | 「[[TP-Project-Plan]]」V2.3   | ① 周计划“改进事项”与“技术债”查询结果分栏显示<br>② 查询代码行数压缩至<10行                                  |     |
| 2   | 「[[TP-Project-Review]]」V2.3 | ① 不显示“空任务”、无重复查询结果<br>② 任务范围覆盖”技术债“<br>③ “成果统计”与“任务统计”查询代码行数压缩至<10行           |     |
| 3   | 「[[TP-Project-Do]]」V2.2     | ① “任务安排”中的“今日代办”与“未规划”代码行数压缩至<10行                                             |     |
| 4   | 「[[TP-Project-Retro]]」V1.4 | ① “类型分析”中“技术债”查询代码行数压缩至<10行<br>② “改善回顾”查询代码行数压缩至<10行                          |     |
| 5   | 「[[TP-Project-Home]]」V2.1     | ① “项目进度”、“交付异常”查询代码行数行数压缩至<10行                                                |     |
| 6   | 「[[@PKMS指导说明]]」            | ① 包含系统核心组件、辅助组件的功能、填写要求说明<br>② 系统运行核心工作流说明及核心组件调用示意图<br>③ 系统运行辅助工作流说明及组件调用示意图 |     |
# 3. 优化与债务
> [!dashboard]
> 
> > [!todo] `$= "[[2-项目/" + dv.current().file.path.split("/")[1] + "/" + "改进待办事项" + "| 改进事项]]"`
> > ```tasks
> >preset project_improvements
> > ```
> 
> > [!tip] 技术债
> >```dataviewjs
> >await dv.view("0-辅助/Views/techDebtValidation", { dv })
> >```

# 4. 任务拆解

- [x] 修改`Plan-2025-WK35.md`文件“改进事项”与“技术债”查询布局 ⏳ 2025-08-28 ✅ 2025-08-28
- [x] 修复[[2-项目/PKMS/3-每周评审/Review-2025-WK33]]文件中“成果统计”与“任务统计”查询异常点（“空任务”、“成果内容重复“、”技术债任务未覆盖“） ⏳ 2025-08-29 ✅ 2025-08-29
- [x] 压缩`Plan-2025-WK35.md`文件“改进事项”与“技术债”查询代码行数 ⏳ 2025-08-29 ✅ 2025-08-29
- [x] 更新「[[TP-Project-Plan]]」 ⏳ 2025-08-29 ✅ 2025-08-29
- [x] 压缩[[2-项目/PKMS/3-每周评审/Review-2025-WK33]]文件中“成果统计”与“任务统计”查询代码行数 ⏳ 2025-08-29 ✅ 2025-08-29
- [x] 更新「[[TP-Project-Review]]」 ⏳ 2025-08-29 ✅ 2025-08-29
- [x] 压缩当日“每日执行”文件“任务安排”中的查询代码行数 ⏳ 2025-08-29 ✅ 2025-08-29
- [x] 更新「[[TP-Project-Do]]」 ⏳ 2025-08-29 ✅ 2025-08-29
- [x] 压缩「[[Retro-2025-WK33]]」“技术债”查询代码行数 ⏳ 2025-08-29 ✅ 2025-08-29
- [x] 压缩「[[Retro-2025-WK33]]」“改善回顾”查询代码行数 ⏳ 2025-08-29 ✅ 2025-08-29
- [x] 更新「[[TP-Project-Retro]]」 ⏳ 2025-08-29 ✅ 2025-08-29
- [x] 压缩「[[PKMS-首页]]」“项目进度”、“交付异常”查询代码行数 ⏳ 2025-08-29 ✅ 2025-08-29
- [x] 更新「[[TP-Project-Home]]」 ⏳ 2025-08-29 ✅ 2025-08-29
- [x] 使用Claude code 初步编写系统核心组件、辅助组件的功能、填写要求说明 ⏳ 2025-08-29 ✅ 2025-08-29
- [x] 使用Claude code 初步编写核心工作流、辅助工作流 ⏳ 2025-08-29 ✅ 2025-08-30
- [ ] 手动优化调整「[[@PKMS指导说明]]」组件相关内容 ⏳ 2025-08-29
- [ ] 优化优化调整「[[@PKMS指导说明]]」工作流相关内容
- [ ] 创建核心工作流、辅助工作流流程图
- [ ] 更新核心工作流、辅助工作流流程图至「[[@PKMS指导说明]]」
- [ ] 优化「[[@PKMS指导说明]]」整体内容（审查逻辑合理性、内容完整性）