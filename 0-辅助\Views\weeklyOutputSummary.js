function weeklyOutputSummary({ dv }) {
    const fileName = dv.current().file.name;
    const weekMatch = fileName.match(/Review-(\d{4})-WK(\d+)/);
    
    if (!weekMatch) {
        dv.paragraph("**错误**：文件名格式不符合要求：Review-YYYY-WKWW");
        return;
    }
    
    const year = parseInt(weekMatch[1]);
    const currentWeek = parseInt(weekMatch[2]);
    const projectName = dv.current().file.path.split("/")[1];
    const dailyNotesPath = `2-项目/${projectName}/2-每日执行`;
    
    function getISOWeek(date) {
        const d = new Date(date);
        d.setHours(0,0,0,0);
        d.setDate(d.getDate() + 4 - (d.getDay() || 7));
        const yearStart = new Date(d.getFullYear(),0,1);
        return Math.ceil(((d - yearStart) / 86400000 + 1)/7);
    }
    
    const allEntriesSet = new Set();
    const dailyNotes = dv.pages(`"${dailyNotesPath}"`).file;
    
    for (let file of dailyNotes) {
        const dateMatch = file.name.match(/Do-(\d{4})-(\d{2})-(\d{2})/);
        if (!dateMatch) continue;
        
        const dateStr = `${dateMatch[1]}-${dateMatch[2]}-${dateMatch[3]}`;
        const fileDate = new Date(dateStr);
        if (isNaN(fileDate.getTime())) continue;
        
        if (fileDate.getFullYear() === year && getISOWeek(fileDate) === currentWeek) {
            if (file.lists) {
                file.lists.forEach(list => {
                    if (list.header?.subpath?.includes("输出") && 
                        list.task === false && 
                        list.text) {
                        allEntriesSet.add(list.text.trim());
                    }
                });
            }
        }
    }
    
    const allEntries = Array.from(allEntriesSet);
    const totalEntries = allEntries.length;
    
    if (totalEntries > 0) {
        allEntries.sort((a, b) => a.localeCompare(b, 'zh-CN'));
        const listContent = allEntries.map(entry => `- ${entry}`).join("\n");
        dv.paragraph(`本周共完成 ${totalEntries} 项输出\n${listContent}`);
    } else {
        dv.paragraph("本周没有找到任何输出条目");
    }
}

weeklyOutputSummary(input);