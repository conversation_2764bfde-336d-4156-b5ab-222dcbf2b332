// 测试修复后的替换逻辑

const templateContent = `| 序号  | 成果（✅） | 关联KR | 价值描述 | 风险预警 | 调整建议 |
| --- | :---- | ---- | ---- | ---- | ---- |
| 1   |       |      |      |      |      |`;

const projectName = 'PKMS';

console.log('=== 原始内容 ===');
console.log(templateContent);

console.log('\n=== 修复后的替换 ===');

// 使用HTML实体替代|字符
let modifiedContent = templateContent.replace(
  /关联KR/g,
  `[[${projectName}-首页#1. OKR设定&#124;关联KR]]`
);

console.log('替换后的内容:');
console.log(modifiedContent);

console.log('\n=== 表格结构检查 ===');
const lines = modifiedContent.split('\n');
lines.forEach((line, index) => {
  if (line.includes('关联KR')) {
    console.log(`行 ${index + 1}: ${line}`);
    const parts = line.split('|');
    console.log(`  列数: ${parts.length}`);
    console.log(`  各列:`, parts.map(p => p.trim()));
  }
});