# addTask 脚本使用说明

## 功能概述

addTask 脚本提供基于当前项目上下文的快速任务添加能力，支持文本选中和手动输入两种模式，自动定位目标周计划文件并标准化插入任务项。

| 操作类型 | 功能描述 | 技术实现 |
|---------|----------|----------|
| 内容输入 | 支持文本选中/手动输入双模式 | `tp.file.selection()` + 输入弹窗 |
| 文件定位 | 自动识别打开的Plan-YYYY-WKXX文件 | 正则表达式匹配 + 多文件选择器 |
| 格式标准化 | 统一Tasks插件兼容格式 | `- [ ] 任务内容` 标准语法 |
| 位置精确定位 | 插入"任务拆解"部分末尾 | 标题正则匹配 + 内容定位算法 |

## 文件结构

```
0-辅助/
├── Scripts/
│   ├── addTask.js          # 核心脚本实现
│   └── addTask脚本说明.md   # 本文档
└── Templater/
    └── Function/
        └── addTask.md      # Templater调用接口
```

## 快速开始

### 1. 环境要求

- **Obsidian版本**：≥ 0.15.0
- **依赖插件**：Templater ≥ 1.16.0
- **文件要求**：已打开符合命名规范的周计划文件

### 2. 快捷键配置

**配置路径**：Obsidian设置 → 快捷键 → 搜索"addTask"

**推荐组合**：`Alt + 3`（符合项目快捷键规范）

### 3. 标准流程

```
触发快捷键 → 内容获取(选中/输入) → 目标文件选择 → 任务项插入 → 成功确认
```

**步骤说明**：
1. 在任意文件中按下快捷键
2. 自动获取选中内容或弹出输入框
3. 自动定位或选择目标周计划文件
4. 在"任务拆解"部分末尾添加任务项
5. 显示操作结果通知

## 功能详解

### 2.1 内容输入模式

#### 模式A：文本选中输入
- **触发条件**：运行前选中文本内容
- **处理逻辑**：自动获取选中内容作为任务描述
- **优势**：快速转换现有文本为任务项
- **格式保持**：保留原始文本格式和特殊字符

#### 模式B：手动输入弹窗
- **触发条件**：无文本选中状态
- **处理逻辑**：弹出对话框接收用户输入
- **输入验证**：空内容检测，自动过滤首尾空白字符
- **取消处理**：支持用户取消操作，无侵入式退出

### 2.2 目标文件定位

#### 文件识别算法
```javascript
const planRegex = /^Plan-\d{4}-WK\d{2}$/;
const isValidPlanFile = (file) => {
    return planRegex.test(file.basename);
};
```

#### 多文件处理策略
| 场景 | 处理逻辑 | 用户交互 |
|------|----------|----------|
| 单个匹配文件 | 自动选择，无弹窗 | 直接执行插入 |
| 多个匹配文件 | 弹出选择器 | 列表选择目标文件 |
| 无匹配文件 | 错误提示 | 显示指导性信息 |

### 2.3 内容插入算法

#### 定位策略
```javascript
const taskHeadingIndex = content.indexOf("任务拆解");
if (taskHeadingIndex === -1) {
    new Notice(`在 ${file.name} 中未找到"任务拆解"标题`);
    return false;
}
```

#### 插入规则
- **位置精确性**：目标章节末尾，保持现有任务顺序
- **格式标准化**：Tasks插件兼容语法 `- [ ] 任务描述`
- **换行处理**：智能处理前后换行符，保持文档格式一致性
- **冲突避免**：避免重复插入相同任务内容

## 错误处理

### 3.1 异常分类

| 错误类型 | 错误代码 | 触发条件 | 处理策略 |
|----------|----------|----------|----------|
| `NO_PLAN_FILE` | 1001 | 未找到周计划文件 | 提示用户打开目标文件 |
| `INVALID_SECTION` | 1002 | 未找到"任务拆解"章节 | 提示检查文件格式完整性 |
| `USER_CANCELLED` | 1003 | 用户取消输入/选择 | 静默退出，无错误提示 |
| `INSERT_FAILED` | 1004 | 内容插入失败 | 控制台输出详细错误信息 |

### 3.2 调试信息

**日志级别**：INFO / WARN / ERROR

```javascript
console.log("[addTask] 选中内容:", selectedText);
console.warn("[addTask] 多个计划文件找到:", planFiles.length);
console.error("[addTask] 插入失败:", error.message);
```

**调试模式**：通过控制台查看详细执行流程和性能指标

## 技术实现

### 4.1 核心算法

**文件筛选逻辑**：
```javascript
async function findPlanFiles() {
    const planFiles = [];
    const planRegex = /^Plan-\d{4}-WK\d{2}$/;
    
    // 获取所有打开的标签页
    const leaves = app.workspace.getLeavesOfType("markdown");
    
    for (const leaf of leaves) {
        const file = leaf.view?.file;
        if (file && planRegex.test(file.basename)) {
            planFiles.push(file);
        }
    }
    
    return planFiles;
}
```

**内容插入逻辑**：
```javascript
async function addTaskToPlan(file, taskContent, originalSelection, activeEditor) {
    try {
        const content = await app.vault.read(file);
        const taskHeadingIndex = content.indexOf("任务拆解");
        // ... 定位和插入逻辑
    } catch (error) {
        console.error("添加任务到文件时出错:", error);
        throw error;
    }
}
```

### 4.2 配置参数

**文件路径配置**：
- 目标文件：已打开的 Plan-YYYY-WKXX.md 文件
- 插入位置："任务拆解"章节末尾
- 任务格式：`- [ ] {任务内容}`

## 扩展指南

### 5.1 功能定制

**添加新操作类型**：
1. 修改内容输入逻辑分支
2. 添加对应的文件处理逻辑
3. 配置新插入格式和位置

**修改文件格式**：
- 调整任务前缀符号
- 更改标题定位策略
- 自定义内容模板结构

### 5.2 性能优化建议

**缓存机制**：考虑添加已打开文件列表缓存
**异步处理**：文件操作保持异步，避免界面卡顿
**错误重试**：关键操作添加失败重试机制

## 使用限制

- **文件状态要求**：目标周计划文件必须处于打开状态
- **章节依赖**：文件必须包含"任务拆解"章节标题
- **并发限制**：不支持多实例同时运行
- **权限要求**：需要文件编辑权限
- **Obsidian环境**：必须在Obsidian应用中运行

## 技术支持

如遇问题，请检查：
1. Templater插件是否启用
2. 周计划文件是否已打开
3. 文件是否包含"任务拆解"章节
4. 控制台错误信息

## 更新日志

### V1.1.1（2025-9-11）：通知提醒优化
- 🔧 **移除冗余提醒**：移除不必要的成功提醒，静默执行，仅保留异常部分

### v1.1.0（2025-08-15）：功能优化
- ✨ **多文件支持**：新增多个周计划文件时的选择功能
- 🔧 **错误处理增强**：完善了文件定位和插入的错误处理机制
- 🔧 **用户体验提升**：更清晰的操作反馈和错误提示
- 🔧 **名称统一**：脚本、模板名称统一

### v1.0.0（2025-07-01）：初始版本发布
- 🎉 **初始版本发布**：支持快速任务添加功能
- ✅ **基本功能**：文本选中输入和手动输入模式
- ✅ **自动文件定位**：基于已打开文件自动定位目标文件
- ✅ **标准化格式**：使用Tasks插件兼容语法