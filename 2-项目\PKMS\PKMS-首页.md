---
startDate: 2025-05-26
endDate: 2025-06-30
---
# 1. OKR设定

- O：搭建个人知识管理系统
- KR1：完成核心组件体系建设，实现项目管理、技术债管理、阻碍管理3大核心组件及知识管理辅助组件稳定运行
- KR2：建立完整工作流体系，覆盖项目生命周期、技术债管理、阻碍处理、知识积累4个核心工作流
- KR3：达成系统成熟应用状态，用户操作流畅度90%以上，认知负担显著降低

# 2. 项目进展

```dataviewjs
await dv.view("0-辅助/Views/weeklyKrProgress", { dv });
```
# 3. 交付异常

```dataviewjs
await dv.view("0-辅助/Views/deliveryAnomalies", { dv });
```

# 4. 变更控制

| 变更日期       | 变更内容                        | 类型  | 决策依据                                                | 决策失误原因                                             | 关键证据                          | 新行动                           |
| ---------- | --------------------------- | --- | --------------------------------------------------- | -------------------------------------------------- | ----------------------------- | ----------------------------- |
| 2025-06-07 | 利用「仓储知识引擎」项目完成PKMS知识模块工作流验证 | 删除  | 三大类知识笔记割裂严重，无法自然联结且WK24已证伪「陈述性知识」+「程序性知识」+「策略性知识」框架 | 组件和工作流探索阶段的不确定性                                    | [[Review-2025-WK24]]          | 探索其他工作流                       |
| 2025-07-09 | 「每周计划」阻碍回顾范围探索              | 删除  | 个人工作场景下，「首次应急处理」+「回顾阶段系统分析」两阶段模型即可完成阻碍的处理，发挥其作用     | 阻碍处理的核心目的理解不准确，未对周评审环节造成严重影响，但导致周回顾环节无法进行改善分析与方案制定 | [[blocker-20250707-02]]@洞见/发现 | 建立「改进待办事项」工作流，以补全计划、闭环系统性改善流程 |
| 2025-07-10 | 组件看板优化（代码优化、误触问题）           | 冻结  | 当前技术手段无法解决误触问题                                      |                                                    |                               |                               |
| 2025-07-15 | 探索降低脚本使用认知负担的方案             | 冻结  | 当前脚本数量较少，覆盖场景不全且尚有脚本未开发                             |                                                    |                               |                               |
