/* ======= 仪表盘核心框架 - 美化版 ======= */
.callout[data-callout="dashboard"] {
    --callout-icon: lucide-layout-dashboard;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
}

.callout[data-callout="dashboard"]>.callout-content {
    display: flex;
    gap: 2em;
    padding: 0;
}

.callout[data-callout="dashboard"]>.callout-title {
    display: none !important;
}

/* 内层callout应用样式 - 移除悬停效果 */
.callout[data-callout="dashboard"] .callout-content>.callout {
    background: linear-gradient(135deg, rgba(var(--callout-color), 0.03) 0%, rgba(var(--callout-color), 0.06) 100%);
    border-left: 3px solid rgba(var(--callout-color), 0.8);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
    padding: 1.25em 1.5em;
    flex: 1;
}

/* ======= 内容优化 - 美化版 ======= */
.dashboard .markdown-embed-content h1 {
    display: none !important;
}

.dashboard .callout {
    background-color: rgba(var(--callout-color), 0.05);
    border-radius: 8px;
    border-left-width: 2px;
}

.dashboard .markdown-embed {
    border-left: none;
    padding-left: 0;
    border-radius: 8px;
    overflow: hidden;
}

/* ======= 响应式元数据布局 - 美化版 ======= */
.dashboard .metadata-properties {
    display: grid;
    gap: 1.5em;
    padding: 1.2em;
    background: rgba(var(--background-secondary-rgb), 0.7);
    border-radius: 8px;
}

.dashboard .metadata-property {
    padding: 12px 15px;
    background: rgba(var(--background-primary-rgb), 0.95);
    border-radius: 6px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

/* 响应式列数设置 */
.dashboard.db-2c .metadata-properties {
    grid-template-columns: repeat(2, 1fr);
}

.dashboard.db-3c .metadata-properties {
    grid-template-columns: repeat(3, 1fr);
}

.dashboard.db-4c .metadata-properties {
    grid-template-columns: repeat(4, 1fr);
}

.dashboard.db-5c .metadata-properties {
    grid-template-columns: repeat(5, 1fr);
}

/* ======= 插件支持 - 美化版 ======= */
.dashboard div.dataview-error-box {
    display: none;
}

.dataview.list-view-ul {
    list-style: none;
    padding-left: 0;
    border-radius: 8px;
    overflow: hidden;
}

ul.dataview.list-view-ul li {
    padding: 14px 18px;
    border-bottom: 1px solid rgba(var(--background-tertiary-rgb), 0.6);
    background: rgba(var(--background-primary-rgb), 0.95);
}