## master管理

- 进入需要管理的文件夹

- 执行初始化命令
```
	git init
```

- 管理目录下的文件状态
```
	git status
	注：新增文件和修改后的文件都是红色
```

- 管理指定文件
```
	git add 文件名（含后缀）
	git add . 当前目录下的所有文件
	注：指定后的文件都是绿色
```

- 管理文件从暂存区回到工作区（绿 -->红）
```
	git reset HEAD <file> //单个文件
	git reset HEAD . //所有文件
	注：如果仓库是新建的且从未提交过，`git reset -- .`或`git reset -- <file>`
```

- 个人信息配置：用户名、邮箱（一次即可）
```
	git config --global user.email "<EMAIL>"
	git config --global user.name "Your Name"
```

- 生成版本
```
	git commit -m '描述信息'
```

- 查看版本记录
```
	git log --oneline
```

- 回滚到之前的版本
```
	git log --oneline
	git reset --hard 版本号 //彻底删除上一个提交（包括工作区修改）
	git reset --soft 版本号 //保留修改但撤销提交
```

- 回滚到之后的版本
```
	git reflog --oneline
	git reset --hard 版本号
```

## branch（分支）

- 分支可以给开发者提供多个独立的环境，意味着你可以把工作从开发主线上分离出来。
```mermaid
	graph LR
    A[主分支] -->|创建| B(功能分支)
    B -->|开发完成| C[[合并到主分支]]
    C -->|触发| D[[CI/CD测试]]
    D -->|成功| E[部署生产]
    D -->|失败| B
    E --> F[[打版本标签]]

    classDef main fill:#f0f0f0,stroke:#555;
    class A main
```
- 查看分支
```
	git branch
```

- 创建分支
```
	git branch '分支名称'
```

- 切换分支
```
	git checkout '分支名称'
```

- 分支合并
```
	git merge '被合并的分支名称'
	注：切换分支再合并，可能产生冲突
```

- 删除分支
```
	git branch -d '被删除的分支名称'
```

## 忽略指定文件夹

- 在项目文件夹（根目录）下创建`.gitignore`文件（每一行表示一条忽略规则）
- 忽略特定路径下的文件夹
```plaintext
	/build/
```
- 将`.gitignore`文件需提交到仓库中