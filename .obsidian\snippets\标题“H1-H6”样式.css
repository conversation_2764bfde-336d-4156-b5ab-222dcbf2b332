/* headers */
/*编辑状态是否显示H1 H2标记*/
/*适配Live preview模式*/
div:not(.cm-active).cm-line span:not(.cm-formatting-header):not(.cm-hashtag):not(.cm-inline-code):not(.cm-highlight).cm-header::before {
  font-size: 0.6rem;
  width: auto;
  margin-right: 1px;
  border-radius: var(--radius-xs);
  color: var(--text-muted);
  overflow: visible;
  font-family: var(--font-default);
  font-weight: normal !important;
}

div.mod-cm6.is-live-preview div:not(.cm-active).cm-line .cm-header ~ span.cm-header::before,
div.mod-cm6.is-live-preview div:not(.cm-active).cm-line .cm-header.cm-hmd-internal-link::before {
  display: none;
}

div:not(.cm-active).cm-line span:not(.cm-formatting-header).cm-header-1::before {
  content: 'H1';
  margin-top: calc(var(--h1-size) - 0.1em);
}

div:not(.cm-active).cm-line span:not(.cm-formatting-header).cm-header-2::before {
  content: 'H2';
  margin-top: calc(var(--h2-size) - 0.2em);
}

div:not(.cm-active).cm-line span:not(.cm-formatting-header).cm-header-3::before {
  content: 'H3';
  margin-top: calc(var(--h3-size) - 0.2em);
}
div:not(.cm-active).cm-line span:not(.cm-formatting-header).cm-header-4::before {
  content: 'H4';
  margin-top: calc(var(--h4-size) - 0.3em);
}

div:not(.cm-active).cm-line span:not(.cm-formatting-header).cm-header-5::before {
  content: 'H5';
  margin-top: calc(var(--h5-size) - 0.4em);
}

div:not(.cm-active).cm-line span:not(.cm-formatting-header).cm-header-6::before {
  content: 'H6';
  margin-top: calc(var(--h6-size) - 0.4em);
}

/*适配source mode 模式*/
div.mod-cm6:not(.is-live-preview) div:not(.cm-active).cm-line span:not(.cm-formatting-header).cm-header ~ span.cm-header::before
{
  display: none;
}
 div.mod-cm6:not(.is-live-preview) div:not(.cm-active).cm-line span.cm-formatting-header {
  display:none;
}
/*适配传统模式*/
div:not(.CodeMirror-activeline)>.CodeMirror-line.hmd-inactive-line span.cm-formatting-header::before {
  position: absolute;
  margin-top: 10px;
  font-size: 0.7rem;
  width: auto;
  margin-left: -18px;
  padding: 0px 2px;
  border-radius: var(--radius-xs);
  color: var(--text-muted);
  overflow: visible;
  font-family: var(--font-default);
  font-weight: normal !important;
}
div:not(.CodeMirror-activeline)>.CodeMirror-line:not(.hmd-inactive-line) span.cm-formatting-header::before {
  position: absolute;
  margin-top: 10px;
  font-size: 0.7rem;
  width: auto;
  margin-left: -6px;
  border-radius: var(--radius-xs);
  color: var(--text-muted);
  overflow: visible;
  font-family: var(--font-default);
  font-weight: normal !important;
}

div:not(.CodeMirror-activeline)>.CodeMirror-line span.cm-formatting-header {
  color: transparent !important;
  background: none;
}

div:not(.CodeMirror-activeline)>.CodeMirror-line span.cm-formatting-header-1::before {
  content: 'H1';
}
div:not(.CodeMirror-activeline)>.CodeMirror-line span.cm-formatting-header-2::before {
  content: 'H2';
}
div:not(.CodeMirror-activeline)>.CodeMirror-line span.cm-formatting-header-3::before {
  content: 'H3';
}
div:not(.CodeMirror-activeline)>.CodeMirror-line span.cm-formatting-header-4::before {
  content: 'H4';
}
div:not(.CodeMirror-activeline)>.CodeMirror-line span.cm-formatting-header-5::before {
  content: 'H5';
}
div:not(.CodeMirror-activeline)>.CodeMirror-line span.cm-formatting-header-6::before {
  content: 'H6';
}