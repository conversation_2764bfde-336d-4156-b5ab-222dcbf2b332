function techDebtValidation({ dv }) {
  const C = {
    types: { presets: ["局部债", "系统债"], get set() { return new Set(this.presets); } },
    priorities: { presets: ["P0", "P1", "P2"], get set() { return new Set(this.presets); } }
  };

  const p = dv.current().file.path.split("/")[1],
    f = `3-过程资产/${p}/技术债`,
    files = dv.pages(`"${f}"`).filter(p => 
      p.file.name.match(/^td-\d{8}-\d{2}$/) && 
      p.status !== "已关闭" &&
      p.file.name !== "dashboard"
    );

  const invalid = (f) => {
    const t = f.type?.toString(), pr = f.priority?.toString();
    return !C.types.set.has(t) || !C.priorities.set.has(pr);
  };

  const invalids = files.filter(invalid);

  const getType = (f) => {
    const t = f.type?.toString();
    return !t ? "(未设置类型)" : C.types.set.has(t) ? t : "(无效类型)";
  };

  const getPriority = (f) => {
    const pr = f.priority?.toString();
    return !pr ? "(未设置优先级)" : C.priorities.set.has(pr) ? pr : "(无效优先级)";
  };

  if (invalids.length > 0) {
    const items = invalids.map(f => {
      const n = f.aliases || f.file.name;
      const t = getType(f);
      const pr = getPriority(f);
      return `[[${f.file.name}|${n}]] - ${t} - ${pr}`;
    });
    dv.paragraph(items);
  } else {
    const valid = files;
    let tType = null;
    for (const t of C.types.presets) {
      if (valid.some(p => p.type === t)) {
        tType = t;
        break;
      }
    }

    if (!tType) {
      dv.paragraph("⚠️ 没有待处理的技术债");
    } else {
      const filtered = valid
        .filter(p => p.type === tType)
        .sort(p => p.priority === "P0" ? 1 : p.priority === "P1" ? 2 : p.priority === "P2" ? 3 : 999);

      if (filtered.length === 0) {
        dv.paragraph(`⚠️ 没有${tType}类型的技术债`);
      } else {          
        const grouped = {};
        filtered.forEach(f => {
          const pr = f.priority || "未设置";
          if (!grouped[pr]) grouped[pr] = [];
          grouped[pr].push(f);
        });

        const lines = [];
        C.priorities.presets.forEach(pr => {
          if (grouped[pr]) {
            grouped[pr].forEach(f => {
              const n = f.aliases || f.file.name;
              lines.push(`[[${f.file.name}|${n}]] - ${f.type} - ${pr}`);
            });
          }
        });
        dv.paragraph(lines);
      }
    }
  }
}

techDebtValidation(input);