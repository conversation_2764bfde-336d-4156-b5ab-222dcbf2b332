# PKMS系统说明

## 1. 系统概述

**核心价值**：是一个基于"OKR+个人敏捷"知识管理系统，采用模块化设计，支持项目生命周期管理、技术债管理、阻碍处理和知识积累的完整工作流。

**文件结构**

```
0-辅助/
├── Scripts/                   # Templater用户脚本（5个核心功能）
├── Templater/                 # 模板和调用接口
│   ├── Function/              # 脚本调用接口
│   └── Notes/                 # 模板文件
├── Help/                      # 帮助文档
├── Views/                     # DataviewJS视图组件
1-Inbox/                       # 收件箱（GTD理念）
2-项目/                        # 项目管理文件
└── {项目名称}/                # 具体项目目录
    ├── {项目名}-首页.md       # 项目总览、OKR追踪
    ├── 改进待办事项.md        # 改进事项管理
    ├── 1-每周计划/            # 周计划文件
    ├── 2-每日执行/            # 日执行文件
    ├── 3-每周评审/            # 周评审文件
    └── 4-每周回顾/            # 周回顾文件
3-过程资产/                    # 组织过程资产
└── {项目名称}/                # 项目相关过程资产
    ├── 技术债/                # 技术债记录
    ├── 阻碍/                  # 阻碍问题记录
    └── @其他/                 # 效率工具箱等
4-知识管理/                    # 知识库体系
├── 问题库/                    # 问题知识积累
├── 方法库/                    # 解决方法和技巧
├── 案例库/                    # 实际案例记录
└── SOP库/                     # 标准操作程序
5-资料/                        # 参考资料库
```
## 2. 核心组件

### 2.1 项目管理组件

**核心价值**：实现项目全生命周期闭环管理，通过结构化流程确保目标对齐、执行透明和持续改进。

**管理流程**

```
项目创建      →     每周计划    →      每日执行        →      每周评审       →        每周回顾
   ↓                  ↓                 ↓                      ↓                     ↓
OKR迭代/设定       改善项评估(可选)     任务规划                成果梳理               问题评估
   ↓                  ↓                 ↓                      ↓                     ↓     
项目回顾		       目标设定           任务执行 / 阻碍简记      成果验收 / 异常分析     流程改善
			          ↓                 ↓         ↓             ↓        ↓           ↓
			     制定验收标准           输出    关键阻碍识别    风险评估   阻碍关联    改善回顾
			          ↓                           ↓             ↓        ↓
			       任务拆解                     文档记录       制定改善   根因分析
			                                                             ↓
			                                                           制定行动
```
`注：“变更控制”贯穿于整个迭代中，设置于「项目首页」的目的是为了方便管理`

**主要文件**：
- `{项目名}-首页.md` - 项目总览、OKR追踪与变更控制中心〔`模板：TP-Project-Home`〕
- `Plan-yyyy-WKxx.md` - 周计划制定与任务分解〔`模板：TP-Project-Plan`〕
- `Do-yyyy-mm-dd.md`- 每日执行与交付物记录〔`模板：TP-Project-Do`〕
- `Review-yyyy-WKxx.md` - 周度成果评审与风险识别〔`模板：TP-Project-Review`〕
- `Retro-yyyy-WKxx.md` - 流程改进与经验沉淀〔`模板：TP-Project-Retro`〕

**填写规范**：

① 项目首页
- `startDate`：项目启动日期
- `endDate`：项目计划完成日期
- `OKR设定`
	- `O（目标）`：描述期望达成的方向性成果（示例：打造高效的团队协作流程）
	- `KR（关键结果）`
		- 探索性KR：定性描述探索方向（示例：初步验证XX方案的可行性）
		- 标准KR：量化行动结果（示例：通过XX行动将用户参与度提升20%）
- `变更控制（记录所有重大计划调整）`
	- `变更日期`、`变更内容`、`变更类型`（删除/冻结/调整）
	- `原决策依据`、`失误分析`、`关键证据`、`新行动计划`

② 每周计划
- `周目标`
	- 明确本周核心目标（关联季度OKR），遵循SMART原则（可无需描述目标的截止时间）
	- [[效率工具箱#^002623|明确目标类型]]（开发/学习/探索等）
- `验收标准`
	- `交付物`：
		- 具体可提交的成果（如：需求文档v1.2）
		- 使用双链关联具体成果（如：`「[[TP-Project-Review]]」V2.4`）
	- `标准/通过条件`：明确的质量标准（如：通过团队评审且无重大缺陷）
	- `备注`：记录历史标准的调整原因（如有必要简要描述原标准）
- `任务拆解`
	- 将目标拆解为具体任务（建议每任务≤4小时）
	- 使用双链关联相关任务和资源（如：`[[TR-资源链接]]`）

③ 每日执行
- `今日阻碍`
	- 使用`关键词法`记录当前阻碍
	- 通过双链关联「关键阻碍」（`Ctrl + 2`快速创建；`关键阻碍`--完全阻断当前任务、预计解决时间>2小时、存在衍生风险）
- `关键输出`
	- 重点记录完成的有形成果（文档、代码、设计稿等）
	- 通过`「」`标记可交付成果（示例：完成`「XX功能原型」`）
	- 通过`〔〕`标记可交付成果关键变更点（如：「每周评审」`〔移除”本周阻碍“统计模块〕`）

④ 每周评审
- `KR进度`
	- `成果`：根据“成果验收”逐一梳理、登记可交付成果
	- `关联KR`：明确每个成果贡献的关键结果（KR）
	- `价值描述`：说明成果对OKR的实际影响（数据支持优先）
	- `风险预警`：识别进展偏差和潜在问题
	- `调整建议`：提出下一周的改进方向
- `交付异常--记录未完成事项并标注原因（阻塞/优先级调整/资源问题）`
	- `未完成成果`：明确计划交付但未完成的成果
	- `关键进展`：记录已取得的阶段性进展（即使未完全完成）
	- `阻碍因素`：说明直接阻碍因素、关联「关键阻碍」（如：环境依赖、资源冲突、技术难点、需求变更）
	- `根因分析`：深入分析根本原因（技术预研不足？资源分配不合理？沟通机制失效？）
	- `下一步行动`：制定具体补救措施（注意：应是可执行、可验证的具体行动）

⑤ 每周回顾
- `流程改善（选择1-2个最高优先级的阻碍进行改进）`
	- `问题溯源`：双链引用具体问题实例（如：`[[周评审#异常点]]`）
	- `根因分析`：从系统角度分析问题本质（流程/工具/沟通）
	- `改善行动`：制定具体、可衡量的改进措施（示例：每日站会增加阻塞问题同步环节）
	- `验收标准`：定义改进有效的衡量指标（示例：任务延期率下降15%）
	- `改善结果`：记录后续验证结果（通过/未通过）

### 2.2 阻碍管理组件

**核心价值**：建立敏捷化的阻碍响应机制，快速识别和消除迭代执行中的瓶颈问题，通过科学的优先级评估和闭环管理，最大限度减少对迭代进度和交付价值的影响，确保团队持续流动。

**管理流程**：即时识别 → 影响评估 → 应急响应 → 根因分析 → 根治方案（如需要） → 效果验证〔[[阻碍处理流程图.svg]]〕

| 阶段  | 时间要求  | 关键动作       | 责任角色   | 升级机制                        |
| --- | ----- | ---------- | ------ | --------------------------- |
| 发现  | 即时识别  | 现场记录、影响评估  | 任何项目成员 | 立即记录，不得延误                   |
| 记录  | 2小时内  | 结构化描述、分类标记 | 问题发现人  | `blocker-YYYYMMDD-NN.md` 创建 |
| 应急  | 4小时内  | 临时方案、快速恢复  | 技术负责人  | 方案评审、风险控制                   |
| 分析  | 24小时内 | 根因分析、5Why法 | 相关技术专家 | 分析报告、经验总结                   |
| 根治  | 本周内   | 永久方案、技术债转化 | 指定开发团队 | 纳入技术债管理体系                   |
| 关闭  | 验证完成  | 效果确认、文档归档  | 项目负责人  | 阻碍解除、经验沉淀                   |
**主要文件**：`blocker-YYYYMMDD-NN.md` - 阻碍记录〔`模板：TP-Project-Blocker.md`〕

**填写规范**：

- `元数据`
	- `aliases`：业务友好名称（体现核心问题和对价值流的影响）
	- `createdDate`：阻碍识别时间
	- `status`：当前处理状态（进行中、待验证、已关闭）
	- `type`：阻碍影响类型（`阻塞型`--完全阻断工作流；`价值威胁`--影响交付价值或质量；`符合型`--影响规范符合性）
	- `location`：影响对象链接实例
	- `cssclasses`：元数据布局（`c3`默认三栏布局）
- `问题诊断`
	- `场景描述`：问题发生的具体上下文
	- `触发条件`：问题复现的特定条件
	- `可观测现象`：具体的异常表现
- `临时方案--应急处理措施，快速恢复流程`
	- `生效时间`：方案开始执行时间
	- `目标`：期望达成的应急目标
	- `方案描述`：具体应急行动步骤
	- `决策依据`：选择该方案的考量
	- `已知风险`：可能带来的副作用
	- `处理状态`：方案执行状态跟踪
- `根因分析`：使用5Why分析法追溯根本原因
- `根治方案（可选）`
	- `计划排期`：根治方案计划时间
	- `方案描述`：具体的根治措施（例如：1. 建立生产环境镜像测试沙箱 2. 增加端到端测试用例）
	- `关联技术债`：产生的技术债务链接实例
	- `可选说明`：该阻碍为一次性事件、临时方案可永久生效

### 2.3 技术债管理组件

**核心价值**：将技术债转化为可量化、可追踪的管理资产，通过系统化流程平衡短期交付与长期技术健康度，确保技术投资决策科学透明。（`为了短期加速开发而采取的不最优（或“偷懒”）的技术实现方案，所导致的长期代码质量下降和未来开发成本增加的隐含代价`）

**管理流程**：识别 → 记录 → 评估 →规划 → 偿还 → 验收〔[[技术债处理流程图.svg]]〕

| 环节  | 输入条件      | 处理策略        | 工具支持                   | 成功标准                     |
| --- | --------- | ----------- | ---------------------- | ------------------------ |
| 识别  | 代码异味、性能下降 | 场景分析、影响评估   | 人工识别 + 监控告警            | 问题清晰描述                   |
| 记录  | 确认技术问题    | 结构化描述、分类标记  | `createTechDebt.js`    | `td-YYYYMMDD-NN.md` 文件创建 |
| 评估  | 技术债清单     | ROI分析、优先级排序 | `techDebtReport.js` 视图 | 优先级分数、偿还计划               |
| 规划  | 高优先级技术债   | 纳入迭代计划、资源分配 | 周计划任务拆解                | 具体执行步骤                   |
| 偿还  | 计划任务      | 代码重构、架构优化   | 每日执行跟踪                 | 验收标准达成                   |
| 验收  | 偿还完成      | 效果验证、文档更新   | 每周评审确认                 | 指标改善、文档同步                |
**主要文件**：`td-YYYYMMDD-NN.md` - 技术债记录〔`模板：TP-Project-TechDebt.md`〕

**填写规范**：
- `元数据`
	- `createdDate`：技术债识别日期
	- `aliases`：业务友好名称（简明扼要，体现业务价值）
	- `location`：具体位置链接（代码/文档/系统）
	- `type`：债务影响范围类型（`局部债`-影响单一模块、`系统债`-影响系统间交互）
	- `priority`：处理紧急度（`P0`-阻塞核心流程，需立即处理；`P1`-显著影响体验或效率，高优；`P2`-有影响但可接受，普通优先级）
	- `status`：当前处理状态（`进行中` → `待验证` → `已关闭` ）
	- `relatedDebt`：关联技术债链接
	- `cssclasses`：元数据布局样式（`c4`-默认四栏布局）
- `问题诊断`
	- `类型`：债务产生性质（`原始债务-主动引入`：为赶工期采取临时方案导致；`衍生债-被动产生`：因依赖系统变更或架构演进产生
	- `场景描述`：具体业务场景（说明问题发生的上下文）
	- `可观测现象`：具体异常现象或指标（尽量量化描述，例如：监控显示P99延迟 > 2s，数据库CPU使用率峰值达90%）
	- `关键影响`：对系统或业务的实际影响（分点说明）
	- `触发条件`：问题复现条件（便于验证）
	- `根因分析`：问题根源分析（非表面现象）
- `临时方案--临时缓解措施，快速控制业务影响`
	- `生效时间`：方案实施的具体时间点
	- `行动`：具体临时措施（明确操作步骤）
	- `退出条件`：方案终止条件（避免临时方案长期化）
- `偿还计划--永久解决方案，彻底根除问题`：具体解决措施（任务列表形式）
- `验收清单--定义完成标准，确保问题彻底解决`
	- `性能指标`：积分查询P99延迟 < 100ms（通过监控验证）
	- `质量要求`：缓存命中率 > 90%（持续监控达标）
	- `测试覆盖`：补充缓存失效集成测试用例（通过CI流水线）
	- `文档更新`：更新架构图与API文档（通过团队评审）
	- `监控告警`：配置缓存命中率告警（<80%自动触发）

## 3 辅助组件

### 3.1 改进事项管理

**核心价值**：建立系统化的持续改进机制，有效捕获和落实团队改进灵感，通过优先级评估和闭环跟踪，将优化想法转化为实际行动，提升团队效率和交付质量。

**管理流程**：灵感捕获 → 评估转化 → 优先级排序 → 执行跟踪 → 效果验证〔[[改善工作流程图.svg]]〕

```
灵感捕获 → 评估转化 → 优先级排序 → 执行跟踪 → 效果验证（周评审）
             ↓ 
          改善项梳理 → 无效清理/转化为行动/移动至待观察
```
`注：定期维护、聚焦执行、价值导向，确保改进事项能够有效落地`

**主要文件**：`改进待办事项.md` - 改进事项汇总〔`模板：TP-Project-Improvement.md`〕

**填写规范**：
- `临时记录`
	- `目的`：收集短期内无法立即完成但具有潜在价值的改进想法
	- `填写要求`
		- 及时记录：在日常工作中随时记录改善灵感
		- 简明描述：用1句话清晰说明改进点
		- 利用自动化脚本快速记录
- `可行动项`
	- `目的`：每周回顾结束后，将缓存区的事项评估转化为具体行动项
	- `填写要求`
		- 具体化：将模糊想法转化为可执行的具体行动
		- 优先级排序：按价值度和紧急度降序排列
- `待观察项`
	- `目的`：当前无法处理但具有潜在价值，需要保留以待后续评估
	- `管理要求`
		- 定期回顾：每月回顾一次留观区事项，评估是否可重新激活
		- 注明原因：明确暂缓处理的具体原因
### 3.2 知识管理组件

**功能描述**：
- 结构化知识积累（问题库、方法库、案例库、SOP库）
- 知识检索和复用
- 经验教训沉淀

```
经验获取 → 结构化整理 → 分类存储 → 检索复用 → 持续更新
    ↓         ↓         ↓         ↓         ↓
  实践中   标准格式   知识库   快速查找   迭代优化
  学习       记录       分类       应用       完善
```

**主要文件**：
- `4-知识管理/` - 各类知识库
- `5-资料/` - 参考资料库

**填写要求**：
- 标准化的知识条目格式
- 关键词标签和分类
- 关联关系建立

### 3.3 自动化脚本组件

**核心价值**：通过Templater自定义脚本和标准化模板实现工作流自动化，提供项目管理与文件创建、任务/技术债/阻碍/灵感创建功能，确保操作效率和数据一致性。

**管理流程**：脚本开发 → 模板集成 → 工作流调用

```
手动操作识别 →  脚本开发 →  模板优化 →    工作流集成
      ↓           ↓         ↓           ↓
    重复性       自动化     标准化      效率提升
    任务分析     脚本实现   模板制作     流程优化
```

**主要文件**：
- `0-辅助/Scripts/` - Templater用户脚本（4个核心功能）
  - `addInsight.js` - 快速记录灵感洞见
  - `addTask.js` - 任务添加到周计划
  - `projectManager.js` - 项目管理与文件创建
  - `createIssue.js` - 技术债/阻碍记录创建
- `0-辅助/Templater/Notes` - 模板文件
- `0-辅助/Templater/Function` - 脚本调用接口文档

**核心功能**（快捷键驱动的工作流自动化）
① 项目管理 `Alt + 2`
- `创建新项目`：弹窗输入项目名称 → 创建项目文件夹 → 生成首页文件 → 自动打开
- `每周计划`：创建`Plan-YYYY-WKWW.md` 文件，存于 `1-每周计划/`
- `每日执行`：创建`Do-YYYY-MM-DD.md` 文件，存于 `2-每日执行/`
- `每周评审`：创建`Review-YYYY-WKWW.md` 文件，存于 `3-每周评审/`
- `每周回顾`：创建`Retro-YYYY-WKWW.md` 文件，存于 `4-每周回顾/`

② 任务管理 `Alt + 3`
- 文本选中或手动输入 → 添加到当前周计划「任务拆解」

③ 灵感记录 `Ctrl + 1`
- 快速记录到改进待办事项「临时记录」
- 快速打开「效率工具箱」
- 快速打开「改进待办事项」

④ 阻碍管理 `Ctrl + 2`
- 弹窗输入阻碍别名 → 创建 `blocker-YYYYMMDD-NN.md` → 自动打开

⑤ 技术债管理 `Ctrl + 3`
- 弹窗输入技术债别名 → 创建 `td-YYYYMMDD-NN.md` 文件 → 自动打开
## 5. 系统特性及开发规范

**数据完整性保障**
- 所有文档包含YAML front matter
- 关键字段标准化：status, createdDate, aliases, type
- 状态值规范：待处理、进行中、已关闭

**性能优化**
- 避免过深的文件夹遍历
- 使用视图组件减少重复代码
- 批处理大量文件操作

**DataviewJS开发规范**
- 数据转换：使用`Array.from()`处理查询结果
- 错误处理：完整的try-catch错误边界
- 调试支持：数据类型和数量验证
- 视图组件化：标准化的组件开发和调用模式
---
此文档为PKMS系统的核心组件和工作流说明，为系统使用和维护提供标准化指引。