# 1. 类型分析
> [!dashboard]
> 
> > [!tip] 阻碍
> >```dataviewjs
> >const currentProject = dv.current().file.path.split("/")[1];
> >await dv.view("0-辅助/Views/blockPriority", { dv, moment, folder: `3-过程资产/${currentProject}/阻碍`, sprintLength: 1 });
> >```
> 
> > [!warning] 技术债
> > ```dataviewjs
> >await dv.view("0-辅助/Views/techDebtReport", { dv });
> > ```

# 2. 流程改善

| 来源                      | 根因分析                                  | 改善行动                                                                      | 验收标准                                                                   | 改善结果 |
| ----------------------- | ------------------------------------- | ------------------------------------------------------------------------- | ---------------------------------------------------------------------- | ---- |
| [[blocker-20250602-02]] | 缺乏结构化KR进度描述框架与方法论，导致在回顾环节耗时过长且产出质量不稳定 | ① 采用临时方案：执行“定位KR类型+分类描述”作为新的KR描述的框架<br>② 实施监控：启动4周的监控期，记录每周KR描述不清晰导致的困惑次数 | ①短期验收： 在4周监控期内，出现连续两周的“每周困惑次数”≤ 1。  <br>②长期验收： 成功将临时方案转化为个人经验方法，并关闭此阻碍 |      |

# 3. 改善回顾

```dataviewjs
await dv.view("0-辅助/Views/improveSummary", { dv });
```