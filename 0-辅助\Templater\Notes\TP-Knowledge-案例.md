**1. 背景**

- 核心意图： 当时我们/我想达成什么？ (清晰陈述最初最初想要解决什么痛点、理想状态下希望达成哪些具体指标)
- 环境因素： 在什么情况下？ (描述关键的环境因素：时间、地点、涉及人员/部门/角色、可用资源、时间压力、组织文化氛围、可用预算/人力/技术限制、市场状况等)
- 起点： 起始状态如何？ (描述事情开始时的具体情况、已知信息、已有的基础或限制)

**2. 困境**

- 问题表象： 遇到了什么具体障碍/挑战/意外？ (描述直接引发困境的具体事件、现象或冲突)
- 核心矛盾： 根本的矛盾/冲突点是什么？ (深入分析问题背后的本质原因、相互冲突的目标/利益/需求、或关键的不确定性)
- 潜在后果： 如果什么都不做/做不好，会怎样？ (说明困境带来的风险、损失或负面影响，强调解决的紧迫性和重要性)

**3. 选项**

| 可能方案 | 维度1（权重） | 维度2（权重） | 维度n（权重） | 综合评分 | 难易度   |
| ---- | ------- | ------- | ------- | ---- | ----- |
| A    |         |         |         |      | 高/中/低 |
- 主要的判断依据/标准是什么？ (说明权衡选项时最看重的几个维度及权重，如：成本效益、风险大小、时间紧迫性、价值观/原则、对相关方的影响、长期效果等)
- 当时考虑了哪些可能的方案？ (列出所有被认真考虑过的行动路径，即使是后来被否决的)

**4. 决策**

- 最终选择： 最终选择了哪个方案？ (明确记录最终的决定)
- 关键理由： 做出这个决定的最核心原因是什么？ (聚焦1-3个最关键的因素，说明为什么这个选项最终胜出)
- 执行要点： 决策是如何具体执行的？ (简述执行的关键步骤、负责人、时间点或里程碑，特别是不同于常规操作的部分)

**5. 启发**

- 原则提炼模块： 从中学到的核心原则/通用策略是什么？ (提炼出可迁移到类似情境的抽象规律、方法论或指导思想。例如：“在资源紧张时，优先保障核心功能的MVP发布”)

- 经验教训：
	- 哪些做法被证明是有效的？ (值得在未来复用的成功经验、技巧或行为；[关联方法])
	- 哪些错误/陷阱需要警惕或避免？ (识别出的认知偏差、操作失误、误判或需要规避的风险)

- 认知纠正： 这次经历改变了哪些看法或假设？ (记录对问题本身、相关方、环境或自身能力的新认识或修正的旧有观念)