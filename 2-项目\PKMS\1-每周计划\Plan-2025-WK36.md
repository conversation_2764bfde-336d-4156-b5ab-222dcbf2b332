# 1. 优化与债务
> [!dashboard]
>
> > [!todo] `$= "[[2-项目/" + dv.current().file.path.split("/")[1] + "/" + "改进待办事项" + "| 改进事项]]"`
> > ```tasks
> >preset project_improvements
> > ```
>
> > [!tip] 技术债
> >```dataviewjs
> >await dv.view("0-辅助/Views/techDebtValidation", { dv })
> >```

# 2. 周目标

- 明确、优化PKMS系统工作流

# 3. 验收标准

| 序号  | 交付物                                                                                                                                                                                                                                                            | 标准/通过条件                                                                                | 备注  |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------- | --- |
| 1   | 「[[@PKMS指导说明]]」                                                                                                                                                                                                                                                | ① 包含系统组件（核心组件、辅助组件）内部运行工作流、功能、填写说明<br>② 系统运行核心工作流说明及核心组件调用示意图<br>③ 系统运行辅助工作流说明及组件调用示意图 |     |
| 2   | ①「[[TP-Project-Review]]」V2.4<br>②「[[TP-Project-Do]]」V2.3<br>③「[[TP-Project-Retro]]」V1.4.1<br>④「[[TP-Project-Blocker]]」V1.4<br>⑤「[[TP-Project-TechDebt]]」V1.2<br>⑥「[[TP-Project-Improvement]]」V2.1<br>⑦ 「[[TP-Project-Home]]」V2.2<br>⑧「[[TP-Project-Plan]]」V2.4 | ① 组件内部运行流畅，符合「@PKMS工作流说明」<br>② 无冗余模块或代码                                                |     |
| 3   | 「[[改善工作流程图.svg]]」V1.1                                                                                                                                                                                                                                          | ①  对比「改善流程图.excalidraw」无节点遗漏<br>②  对比「改善流程图.excalidraw」无路径遗漏                           |     |
| 4   | 「[[阻碍处理流程图.svg]]]                                                                                                                                                                                                                                              | ①  对比「阻碍处理流程图.excalidraw」无节点遗漏<br>②  对比「阻碍处理流程图.excalidraw」无路径遗漏                       |     |
| 5   | ①「[[addImprovement.js]]」<br>②「[[addTask.js]]」<br>③「[[createIssue.js]]」<br>⑤「[[projectManager.js]]」                                                                                                                                                             | ① 代码逻辑符合「@PKMS工作流说明」<br>② JS脚本、dataviewjs查询运行正常                                        |     |
# 4. 任务拆解

- [x] 手动优化调整「[[@PKMS指导说明]]」组件相关内容 ⏳ 2025-09-05 ✅ 2025-09-08
- [x] 优化优化调整「[[@PKMS指导说明]]」工作流相关内容 ⏳ 2025-09-05 ✅ 2025-09-08
- [x] 优化「[[@PKMS指导说明]]」整体内容（审查逻辑合理性、内容完整性） ⏳ 2025-09-05 ✅ 2025-09-08
- [x] 更新「[[TP-Project-Review]]」 ⏳ 2025-09-05 ✅ 2025-09-09
- [x] 更新「[[TP-Project-Do]]」 ⏳ 2025-09-05 ✅ 2025-09-09
- [x] 检查模板元数据字段，统一含义相同的字段名称，调整相关JS脚本、查询代码 ⏳ 2025-09-06 ✅ 2025-09-10
- [x] 修改「[[TP-Project-Home]]」文件名称（保持与「[[@PKMS指导说明]]」一致）及相关脚本代码 ⏳ 2025-09-06 ✅ 2025-09-10
- [x] 调整「[[TP-Project-Do]]」模板“输出”模块标题名称（保持与「[[@PKMS指导说明]]」一致） ⏳ 2025-09-05 ✅ 2025-09-09
- [x] 调整「[[TP-Project-Review]]」模板“KR进度”和”交付异常“表格字段名称（保持与「[[@PKMS指导说明]]」一致） ⏳ 2025-09-05 ✅ 2025-09-09
- [x] 调整「[[TP-Project-Retro]]」"流程改善”表格字段名称（保持与「[[@PKMS指导说明]]」一致） ⏳ 2025-09-05 ✅ 2025-09-09
- [x] 调整「[[TP-Project-Blocker]]」模板文件名称、模板内容（保持与「[[@PKMS指导说明]]」一致）及相关脚本代码 ⏳ 2025-09-06 ✅ 2025-09-10
- [x] 调整「[[TP-Project-TechDebt]]」文件名称、模板内容（保持与「[[@PKMS指导说明]]」一致）及相关脚本代码 ⏳ 2025-09-06 ✅ 2025-09-10
- [x] 调整「[[TP-Project-Improvement]]」文件名称，模板内容（保持与「[[@PKMS指导说明]]」一致）及相关脚本代码 ⏳ 2025-09-06 ✅ 2025-09-10
- [x] 测试5个核心自动化脚本及项目组件查询结果 ⏳ 2025-09-06 ✅ 2025-09-10
- [x] 调整「[[TP-Project-Plan]]」模板内容模块排序 ⏳ 2025-09-06 ✅ 2025-09-10
- [x] 合并「技术债处理工作流.svg」与「[[技术债处理流程图.svg]]」 ⏳ 2025-09-06 ✅ 2025-09-10
- [x] 使用drawio插件重新绘制「[[改善工作流程图.svg]]」（参考文件：改善工作流程图） ⏳ 2025-09-06 ✅ 2025-09-10
- [x] 使用drawio插件重新绘制「[[阻碍处理流程图.svg]]]（参考文件：阻碍处理工作流程图） ⏳ 2025-09-06 ✅ 2025-09-10
- [x] 优化阻碍创建的逻辑功能优化：增加文本选中、双链注入 ⏳ 2025-09-07 ✅ 2025-09-10
- [x] 优化脚本说明文档（符合「[[@PKMS工作流说明]]」、版本说明） ⏳ 2025-09-07 ✅ 2025-09-11
- [x] 分析自动化脚本，移除不必要的提醒通知代码 ⏳ 2025-09-07 ✅ 2025-09-11
- [x] 合并技术债、阻碍脚本功能 ⏳ 2025-09-07 ✅ 2025-09-11
- [x] 更新技术文档 ⏳ 2025-09-07 ✅ 2025-09-11
- [-] #AdHoc 修复dataviewjs代码刷新数据后，屏幕闪烁问题 ⏳ 2025-09-07
- [x] 修复项目首页“项目进展”、“交付异常”查询异常BUG ⏳ 2025-09-07 ✅ 2025-09-12
- [x] 记录[[Task插件预设代码片段]] ⏳ 2025-09-07 ✅ 2025-09-12
- [x] 优化[[weeklyTaskStats.js]]增加“已取消”任务统计、取消任务列表功能 ⏳ 2025-09-07 ✅ 2025-09-12
- [ ] 优化[[projectManager.js]]，增加创建自动创建”改善事项“、”关联KR“双链功能 ⏳ 2025-09-07