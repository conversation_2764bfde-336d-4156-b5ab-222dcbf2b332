# 1. 类型分析
> [!dashboard]
> 
> > [!tip] 阻碍
> >```dataviewjs
> >const currentProject = dv.current().file.path.split("/")[1];
> >await dv.view("0-辅助/Views/blockPriority", { dv, moment, folder: `3-过程资产/${currentProject}/阻碍`, sprintLength: 1 });
> >```
> 
> > [!warning] 技术债
> > ```dataviewjs
> >await dv.view("0-辅助/Views/techDebtReport", { dv });
> > ```

# 2. 流程改善

| 问题溯源 | 根因分析 | 改善行动 | 验收标准 | 改善结果 |
| ---- | ---- | ---- | ---- | ---- |
|      |      |      |      |      |

# 3. 改善回顾

```dataviewjs
await dv.view("0-辅助/Views/improveSummary", { dv });
```