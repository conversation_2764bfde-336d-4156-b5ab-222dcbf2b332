/* 核心元数据网格布局 - 仅保留网格布局功能 */
body .markdown-source-view.mod-cm6 .metadata-properties,
body .markdown-preview-view .metadata-properties {
    display: grid;
    gap: 1em;
}

/* 默认列数：1列（当未指定c类时） */
body .markdown-source-view.mod-cm6 .metadata-properties:not([class*="c"]),
body .markdown-preview-view .metadata-properties:not([class*="c"]) {
    grid-template-columns: repeat(1, 1fr);
}

/* 列数控制 */
body .markdown-source-view.mod-cm6.c2 .metadata-properties,
body .markdown-preview-view.c2 .metadata-properties {
    grid-template-columns: repeat(2, 1fr) !important;
}

body .markdown-source-view.mod-cm6.c3 .metadata-properties,
body .markdown-preview-view.c3 .metadata-properties {
    grid-template-columns: repeat(3, 1fr) !important;
}

body .markdown-source-view.mod-cm6.c4 .metadata-properties,
body .markdown-preview-view.c4 .metadata-properties {
    grid-template-columns: repeat(4, 1fr) !important;
}

body .markdown-source-view.mod-cm6.c5 .metadata-properties,
body .markdown-preview-view.c5 .metadata-properties {
    grid-template-columns: repeat(5, 1fr) !important;
}

body .markdown-source-view.mod-cm6.c6 .metadata-properties,
body .markdown-preview-view.c6 .metadata-properties {
    grid-template-columns: repeat(6, 1fr) !important;
}

/* 确保元数据名称和值在同一行显示 */
body .markdown-source-view.mod-cm6 .metadata-property,
body .markdown-preview-view .metadata-property {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    min-width: 0;
    flex-wrap: nowrap;
    height: 100%;
}

body .markdown-source-view.mod-cm6 .metadata-property-name,
body .markdown-preview-view .metadata-property-name {
    font-weight: bold;
    margin-right: 0.5em;
    white-space: nowrap;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    height: 100%;
    line-height: normal;
}

/* 针对编辑模式下的元数据键容器 */
body .markdown-source-view.mod-cm6 .metadata-property-key,
body .markdown-source-view.mod-cm6 .metadata-property-key-input {
    display: flex;
    align-items: center;
    height: 100%;
}

/* 针对预览模式下的元数据键容器 */
body .markdown-preview-view .metadata-property-key {
    display: flex;
    align-items: center;
    height: 100%;
}

body .markdown-preview-view .metadata-property-key .metadata-property-icon {
    display: flex;
    align-items: center;
    margin-right: 0.5em;
}

body .markdown-source-view.mod-cm6 .metadata-property-value,
body .markdown-preview-view .metadata-property-value {
    flex: 1;
    min-width: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
    height: 100%;
}