# projectManager 脚本使用说明

## 功能概述

projectManager 脚本提供完整的项目创建和管理自动化能力，支持五种操作模式：

| 操作类型 | 功能描述 | 目标位置 |
|---------|----------|----------|
| 创建新项目 | 创建新项目文件夹和首页文件 | `2-项目/{项目名称}/{项目名称}-首页.md` |
| 每周计划 | 创建或打开每周计划文件 | `2-项目/{项目名称}/1-每周计划/Plan-YYYY-WKWW.md` |
| 每日执行 | 创建或打开每日执行文件 | `2-项目/{项目名称}/2-每日执行/Do-YYYY-MM-DD.md` |
| 每周评审 | 创建或打开每周评审文件 | `2-项目/{项目名称}/3-每周评审/Review-YYYY-WKWW.md` |
| 每周回顾 | 创建或打开每周回顾文件 | `2-项目/{项目名称}/4-每周回顾/Retro-YYYY-WKWW.md` |

## 文件结构

```
0-辅助/
├── Scripts/
│   ├── projectManager.js          # 核心脚本实现
│   └── projectManager脚本说明.md   # 本文档
└── Templater/
    ├── Function/
    │   └── projectManager.md      # Templater调用接口
    └── Notes/
        ├── TP-Project-Home.md     # 项目首页模板
        ├── TP-Project-Plan.md     # 每周计划模板
        ├── TP-Project-Do.md       # 每日执行模板
        ├── TP-Project-Review.md   # 每周评审模板
        └── TP-Project-Retro.md    # 每周回顾模板
```

## 快速开始

### 1. 快捷键配置

**操作路径**：Obsidian设置 → 快捷键 → 搜索"projectManager"

**推荐快捷键**：`Ctrl + Shift + P`（项目管理英文Project Management首字母）

### 2. 使用流程

```
快捷键触发 → 选择操作类型 → 自动创建/打开文件 → 新标签页打开
```

**步骤说明**：
1. 在Obsidian中按下快捷键
2. 选择操作类型（0-4）
3. 根据选择完成相应操作
4. 脚本自动处理文件创建和打开

## 功能详解

### 3.1 创建新项目

**触发条件**：选择【0】创建新项目

**处理逻辑**：
- 弹窗输入项目名称
- 在`2-项目`目录下创建项目文件夹
- 使用`TP-Project-Home.md`模板创建项目首页文件
- 在新标签页中打开首页文件

**文件创建逻辑**：
```javascript
const projectPath = `2-项目/${name.trim()}`;
await app.vault.createFolder(projectPath);
const file = await createFile(
    `${projectPath}/${name.trim()}-首页.md`,
    "TP-Project-Home"
);
```

### 3.2 每周计划

**触发条件**：选择【1】每周计划

**处理逻辑**：
- 从当前文件路径提取项目名称
- 在`1-每周计划`目录中创建文件
- 文件名格式：`Plan-YYYY-WKWW.md`
- 使用`TP-Project-Plan.md`模板

**周数计算算法**：
```javascript
function getWeekNumber(date) {
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    d.setDate(d.getDate() + 3 - ((d.getDay() + 6) % 7));
    const week1 = new Date(d.getFullYear(), 0, 4);
    return 1 + Math.round(((d - week1) / 86400000 - 3 + ((week1.getDay() + 6) % 7)) / 7);
}
```

### 3.3 每日执行

**触发条件**：选择【2】每日执行

**处理逻辑**：
- 从当前文件路径提取项目名称
- 在`2-每日执行`目录中创建文件
- 文件名格式：`Do-YYYY-MM-DD.md`
- 使用`TP-Project-Do.md`模板

**日期格式处理**：
```javascript
const fileName = `${config.prefix}-${now.toISOString().split("T")[0]}.md`;
```

### 3.4 每周评审

**触发条件**：选择【3】每周评审

**处理逻辑**：
- 从当前文件路径提取项目名称
- 在`3-每周评审`目录中创建文件
- 文件名格式：`Review-YYYY-WKWW.md`
- 使用`TP-Project-Review.md`模板

### 3.5 每周回顾

**触发条件**：选择【4】每周回顾

**处理逻辑**：
- 从当前文件路径提取项目名称
- 在`4-每周回顾`目录中创建文件
- 文件名格式：`Retro-YYYY-WKWW.md`
- 使用`TP-Project-Retro.md`模板

## 错误处理

### 4.1 异常场景

| 错误类型 | 触发条件 | 处理策略 | 用户提示 |
|---------|----------|----------|----------|
| 活动文件缺失 | 无当前活动文件 | 终止执行 | "请先打开一个项目文件" |
| 项目路径无效 | 文件不在项目目录下 | 终止执行 | "请在项目文件夹中执行此操作" |
| 模板文件缺失 | 模板文件不存在 | 终止执行 | "模板文件不存在: {模板路径}" |
| 用户取消输入 | 项目名称对话框取消 | 终止执行 | "项目名称不能为空" |
| 项目已存在 | 创建重复项目 | 终止执行 | "项目"{name}"已存在" |

### 4.2 调试方法

**控制台查看**：`Ctrl + Shift + I` → Console标签页

**关键调试信息输出**：
```javascript
console.log("路径部分:", pathParts);
console.log("项目名称:", projectName);
console.log("文件路径:", filePath);
console.error("操作失败:", error.message);
```

## 技术实现

### 5.1 核心算法

**项目路径解析**：
```javascript
const pathParts = activeFile.path.split("/");
if (pathParts[0] !== "2-项目") {
    new Notice("请在项目文件夹中执行此操作");
    return;
}
const projectName = pathParts[1];
```

**文件存在性检查**：
```javascript
const existingFile = app.vault.getAbstractFileByPath(filePath);
if (existingFile) {
    await app.workspace.getLeaf("tab").openFile(existingFile);
    new Notice(`已打开现有的${fileType}文件`);
    return;
}
```

**模板应用**：
```javascript
async function createFile(filePath, templateName) {
    const templatePath = `0-辅助/Templater/Notes/${templateName}.md`;
    const templateFile = app.vault.getAbstractFileByPath(templatePath);
    const content = await app.vault.read(templateFile);
    return await app.vault.create(filePath, content);
}
```

### 5.2 配置参数

**操作类型配置**：
```javascript
const configs = {
    每周计划: {
        template: "TP-Project-Plan",
        folder: "1-每周计划",
        prefix: "Plan",
    },
    每日执行: {
        template: "TP-Project-Do",
        folder: "2-每日执行",
        prefix: "Do"
    },
    每周评审: {
        template: "TP-Project-Review",
        folder: "3-每周评审",
        prefix: "Review",
    },
    每周回顾: {
        template: "TP-Project-Retro",
        folder: "4-每周回顾",
        prefix: "Retro",
    },
};
```

## 扩展指南

### 6.1 功能定制

**添加新操作类型**：
1. 修改 `tp.system.suggester` 的选项数组
2. 在 `configs` 对象中添加新配置
3. 添加对应的处理逻辑分支

**修改文件命名规则**：
- 调整日期格式或周数计算方式
- 更改文件前缀或后缀
- 添加项目名称前缀

**自定义模板路径**：
```javascript
// 可配置化模板路径
const templateBasePath = config.templatePath || "0-辅助/Templater/Notes/";
```

### 6.2 性能优化建议

**缓存机制**：对项目路径解析结果进行缓存，避免重复计算
**模板预加载**：在脚本初始化时预加载常用模板内容
**批量操作**：支持批量创建多个项目文件
**错误重试**：对文件操作添加重试机制，提高稳定性

## 使用限制

### 7.1 环境依赖

- **Obsidian要求**：必须在Obsidian应用中运行
- **插件依赖**：需要启用Templater插件
- **权限要求**：需要文件创建、读取、写入、文件夹创建权限

### 7.2 路径约束

- **项目结构**：必须使用标准的"2-项目/项目名称/..."目录结构
- **模板位置**：模板文件必须位于`0-辅助/Templater/Notes/`目录
- **文件命名**：遵循固定的文件名格式规则

### 7.3 性能考虑

- **文件操作**：涉及多个异步文件操作，可能有一定延迟
- **模板大小**：模板文件不宜过大，建议保持在10KB以内
- **并发限制**：不建议同时运行多个实例，避免文件冲突

## 技术支持

### 8.1 常见问题排查

**问题**：脚本无法找到当前活动文件
- 检查是否有文件处于活动状态
- 重新打开文件后再尝试运行脚本

**问题**：无法提取项目名称
- 确认文件路径包含"2-项目/项目名称"结构
- 检查文件是否在正确的项目目录下

**问题**：模板文件不存在
- 检查 `0-辅助/Templater/Notes/` 目录下的模板文件是否存在
- 确认模板文件名称正确

**问题**：文件夹创建失败
- 检查是否有足够的文件系统权限
- 确认目标路径不存在非法字符

### 8.2 必需模板文件

确保以下模板文件存在：
- `TP-Project-Home.md` - 项目首页模板
- `TP-Project-Plan.md` - 每周计划模板  
- `TP-Project-Do.md` - 每日执行模板
- `TP-Project-Review.md` - 每周评审模板
- `TP-Project-Retro.md` - 每周回顾模板

## 更新日志

### V1.2.1（2025-9-11）：通知提醒优化
- 🔧 **移除冗余提醒**：移除不必要的成功提醒，静默执行，仅保留异常部分

### v1.2.0（2025-09-11）：文档优化与功能增强
- 📖 **文档重构**：基于addImprovement标准重构使用说明文档
- 🔧 **错误处理**：完善了异常场景处理和用户提示
- 🔧 **性能提示**：添加了性能考虑和优化建议
- 🔧 **使用限制**：明确了环境依赖和路径约束
- 🔧 **名称统一**：脚本、模板名称统一

### v1.1.0（2025-08-15）：功能完善
- ✨ **周数计算**：新增ISO周数自动计算功能
- 🔧 **文件存在检查**：添加了文件存在性检查，避免重复创建
- 🔧 **目录自动创建**：新增目录结构自动创建功能

### v1.0.0（2025-07-01）：初始版本发布
- 🎉 **基本功能**：支持五种操作模式
- ✅ **自动文件创建**：基于模板自动创建项目文件
- ✅ **智能路径解析**：从当前文件路径自动提取项目名称
- ✅ **文件自动打开**：创建后在新标签页中打开
