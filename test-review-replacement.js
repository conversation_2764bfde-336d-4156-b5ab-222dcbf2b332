// 测试脚本：模拟每周评审文件的替换过程

// 模拟模板内容
const templateContent = `# 1. 成果验收
> [!dashboard]
> 
> > [!todo] 成果统计
> >\`\`\`dataviewjs
> >await dv.view("0-辅助/Views/weeklyOutputSummary", { dv });
> >\`\`\`

# 2. KR进度

| 序号  | 成果（✅） | 关联KR | 价值描述 | 风险预警 | 调整建议 |
| --- | :---- | ---- | ---- | ---- | ---- |
| 1   |       |      |      |      |      |

# 3. 交付异常

| 序号  | 未完成成果 | 关键进展 | 阻碍因素 | 根因分析 | 下一步行动 |
| --- | ----- | ---- | ---- | ---- | ----- |
| 1   |       |      |      |      |       |
`;

const projectName = 'PKMS';

console.log('=== 原始模板内容 ===');
console.log(templateContent);

console.log('\n=== 执行替换 ===');

// 模拟当前的替换逻辑
let modifiedContent = templateContent.replace(
  /关联KR/g,
  `[[${projectName}-首页#1. OKR设定|关联KR]]`
);

console.log('替换后的内容:');
console.log(modifiedContent);

console.log('\n=== 检查替换结果 ===');
const lines = modifiedContent.split('\n');
const krLine = lines.find(line => line.includes('关联KR'));
console.log('KR进度表格行:', krLine);

// 检查表格格式
if (krLine) {
  const parts = krLine.split('|');
  console.log('表格列数:', parts.length);
  console.log('各列内容:');
  parts.forEach((part, index) => {
    console.log(`  列 ${index}: "${part.trim()}"`);
  });
}