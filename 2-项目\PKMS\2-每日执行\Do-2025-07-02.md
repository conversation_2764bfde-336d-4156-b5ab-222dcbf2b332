---
homePageLink: "[[PKMS-首页]]"
---
# 1. 任务安排
> [!dashboard]
> 
> > [!todo] 今日代办
> > ```tasks
> > hide backlink
> > filter by function \
> > const fileName = query.file.filenameWithoutExtension;\
> > const dateMatch = fileName.match(/\d{4}-\d{2}-\d{2}/);\
> > if (!dateMatch) return false;\
> > const targetDate = moment(dateMatch[0]);\
> > const year = targetDate.isoWeekYear();\
> > const weekNumber = targetDate.isoWeek().toString().padStart(2, '0');\
> > const targetPath = `Plan-${year}-WK${weekNumber}`;\
> > const pathMatches = task.file.path.includes(targetPath);\
> > const titleKeywords = "任务"; \
> > const headingMatches = task.heading && task.heading.includes(titleKeywords);\
> > let dateMatches = false;\
> > if (task.scheduled && task.scheduled.moment) {\
> > dateMatches = task.scheduled.moment.isSame(targetDate, 'day')}\
> > return pathMatches && headingMatches && dateMatches;
> > ```
> 
> > [!tip] 未规划
> > ```tasks
> > not done
> > no scheduled date
> > hide backlink
> > filter by function \
> > const fileName = query.file.filenameWithoutExtension;\
> > const dateMatch = fileName.match(/\d{4}-\d{2}-\d{2}/);\
> > if (!dateMatch) return false;\
> > const targetDate = moment(dateMatch[0]);\
> > const year = targetDate.isoWeekYear();\
> > const weekNumber = targetDate.isoWeek().toString().padStart(2, '0');\
> > const targetPath = `Plan-${year}-WK${weekNumber}`;\
> > const pathMatches = task.file.path.includes(targetPath);\
> > const titleKeywords = "任务";\
> > const headingMatches = task.heading && task.heading.includes(titleKeywords);\
> > const hasContent = task.description?.trim().length > 0;\
> > return pathMatches && headingMatches && hasContent;
> > ```
> 
> > [!warning] 阻碍
> > ```dataviewjs
> > function displayBlockerFiles() {
> > // 1. 从当前文件名提取目标日期
> > const currentFileName = dv.current().file.name;
> > const dateMatch = currentFileName.match(/Do-(\d{4}-\d{2}-\d{2})/);
> > if(!dateMatch){return; }
> > const targetDate = dateMatch[1];
> > const projectName = dv.current().file.path.split("/")[1];
> > const path = `3-过程资产/${projectName}`;
> > // 2. 创建日期解析函数
> > const parseDate = (value) => {
> > if (value?.isValid) return value; // 已经是 Luxon 对象
> > const dateStr = value?.toString() || "";
> > // 解析 "6 02, 2025" 格式
> > const match = dateStr.match(/(\d{1,2}) (\d{2}), (\d{4})/);
> > if (match) {
> > const [, month, day, year] = match;
> > return dv.date(`${year}-${month.padStart(2, '0')}-${day}`);}
> > // 尝试解析标准日期
> > return dv.date(dateStr);
> > };
> > // 3. 查询目标文件
> > const files = dv.pages(`"${path}"`).filter(p => {
> > // 文件名格式检查
> > if (!p.file.name.match(/^blocker-\d{8}-\d{2}$/)) return false;
> > // 日期解析和比较
> > const createdDate = parseDate(p.created_Date);
> > if (!createdDate?.isValid) return false;
> > const targetDateObj = dv.date(targetDate);
> > return createdDate.hasSame(targetDateObj, "day");});
> > if (files.length > 0) {
> > for (let file of files) {
> > const linkText = file.aliases?.length ? file.aliases[0] : file.file.name;
> > const relation = file.relation ? file.relation : "未设置";
> > dv.paragraph(`- [[${file.file.path}|${linkText}]] ` +`🚦${file.status || "未设置"}` +` 🧷${relation}`);}
> > } else {dv.paragraph(`📭 今日未遇到相关阻碍`)}}
> > displayBlockerFiles();
> > ```
# 2. 输出

- 「技术债」模板
- 「每周回顾」新增“类型分析”