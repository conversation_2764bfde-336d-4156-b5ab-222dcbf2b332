---
aliases:
  - 代码辅助开发工具失效
created_Date: 2025-08-08
status: 进行中
type: 阻塞型
relation:
  - "[[Plan-2025-WK32#^0cfe34]]"
  - "[[Plan-2025-WK32]]"
  - "[[Retro-2025-WK32]]"
cssclasses:
  - c3
---
# 1. 基础信息

- 背景描述：
	- dataviewjs查询结果显示（参考[[blockPriority算法说明]]）[[blocker-20250602-01]]、[[blocker-20250602-02]]的最终优先级结果相同
- 可观测现象：
	- 使用DeepSeek无法定位、修复异常
	- augment code余额不足，且无有效方法白嫖
- 触发条件：
	- 开发代码、优化代码质量
	- 编写技术文档
- 影响：
	- 无法顺利进行代码开发
	- 无法顺利完成成果交付

# 2. 临时方案

| 生效时间                | 目标       | 临时方案描述                                               | 决策依据                      | 已知风险与局限                  | 状态跟踪 | 债务等级 | 知识缺口 |
| ------------------- | -------- | ---------------------------------------------------- | ------------------------- | ------------------------ | ---- | ---- | ---- |
| 2025-08-08 18:30:57 | 迭代临时开发工具 | 使用Kimi（K2模型）辅助代码开发                                   | K2模型在代码开发场景下的性能优于DeepSeek | 1、模型实际使用效果未达预期           | 生效中  |      |      |
| 2025-8-28 14:26:55  | 补充备用开发工具 | ① 使用augment code 作为主要开发工具<br>② 使用Claude code作为备用开发工具 | 当前最接近augment code的开发工具    | Claude code实践经验不足，代码质量较差 | 生效中  |      |      |
# 3. 根因分析

- 核心问题是什么？ --> 

# 5. 最终方案（可选）

| 生效时间 | 根本原因 | 方案描述 | 决策依据 | 已知风险与局限 |
| ---- | ---- | ---- | ---- | ------- |
|      |      |      |      |         |
