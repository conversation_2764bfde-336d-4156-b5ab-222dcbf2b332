---
createdDate: 2025-08-27
aliases:
  - 切换文档卡顿
location:
  - "[[addTask.js]]"
type: 局部债
priority: P1
status: 已关闭
relatedDebt:
  - "[[td-20250826-01]]"
cssclasses:
  - c3
---
# 1. 基础信息

| 序号  | 类型   | 场景描述          | 可观测现象                   | 关键影响            | 触发条件（如何发现？）     | 初步分析/根本原因   |
| --- | ---- | ------------- | ----------------------- | --------------- | --------------- | ----------- |
| 1   | 原始债务 | 使用技术债模板制定偿还计划 | 技术债文件被打开后，切换其他文档会出现明显卡顿 | 记录技术债时流畅度、体验感下降 | 使用自动化脚本自动同步偿还计划 | 全局监听会牺牲系统性能 |

# 2. 应急方案
| 序号  | 生效时间      | 行动                       | 退出条件         |
| --- | --------- | ------------------------ | ------------ |
| 2   | 2025-8-13 | 删除偿还计划表格格式，采用任务了格式手动创建任务 | 偿还计划任务创建脚本重构 |
# 4. 偿还计划

- [-] 移除代码中的全局监听机制，利用task插件API构建局部监听逻辑 ➕ 2025-08-14
- [x] 修改「每日执行」中“未规划”、“今日代办”任务统计逻辑，使其覆盖技术债文件 ➕ 2025-08-14 ⏳ 2025-08-14
- [ ] 

# 5. 验收清单

- [x] 切换文档期间无任何卡顿现象 ✅ 2025-08-28
- [x] 规划“技术债”任务后，可自动纳入本周迭代 ✅ 2025-08-28