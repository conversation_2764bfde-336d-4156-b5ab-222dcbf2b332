/* Dataview 表格列宽优化 - 16px最小宽度 + 内容自适应 */

/* 核心：强制内容完全可见 */
.dataview *,
.block-language-dataviewjs * {
    overflow: visible !important;
    text-overflow: clip !important;
    white-space: normal !important;
    word-break: normal !important;
    max-width: none !important;
}

/* 表格布局：自动列宽 + 最小列宽 */
.dataview table,
.block-language-dataviewjs table {
    table-layout: auto !important;
    width: auto !important;
    min-width: 100% !important;
    border-collapse: collapse !important;
}

/* 列宽控制：最小100px + 内容自适应 */
.dataview col,
.block-language-dataviewjs col {
    min-width: 100px !important;
    width: auto !important;
}

/* 单元格：100px最小宽度 + 紧凑表头 */
.dataview td,
.block-language-dataviewjs td {
    min-width: 100px !important;
    width: auto !important;
    max-width: none !important;
    padding: 6px 12px !important;
    vertical-align: middle !important;
    white-space: normal !important;
    word-break: normal !important;
}

/* 表头：8px上下padding增加高度 - 保持其他样式不变 */
.dataview th,
.block-language-dataviewjs th {
    min-width: 100px !important;
    width: auto !important;
    max-width: none !important;
    padding: 8px 12px !important;
    vertical-align: middle !important;
    white-space: normal !important;
    word-break: normal !important;
    line-height: 2 !important;
    font-weight: 600 !important;
}

/* 深层选择器：确保所有层级应用规则 */
.dataview .table-view-table col,
.block-language-dataviewjs .table-view-table col,
.dataview .table-view-table td,
.block-language-dataviewjs .table-view-table td,
.dataview .table-view-table th,
.block-language-dataviewjs .table-view-table th {
    min-width: 100px !important;
    width: auto !important;
    max-width: none !important;
}

/* 主题兼容：继承变量但保持宽度规则 */
.dataview,
.block-language-dataviewjs {
    color: var(--text-normal) !important;
    font-family: var(--font-text) !important;
}

/* 边框：清晰但不影响宽度计算 */
.dataview table,
.block-language-dataviewjs table {
    border: 1px solid var(--background-modifier-border) !important;
}

.dataview td,
.dataview th,
.block-language-dataviewjs td,
.block-language-dataviewjs th {
    border: 1px solid var(--background-modifier-border) !important;
}