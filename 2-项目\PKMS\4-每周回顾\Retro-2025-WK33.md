# 1. 类型分析
> [!dashboard]
> 
> > [!tip] 阻碍
> >```dataviewjs
> >const currentProject = dv.current().file.path.split("/")[1];
> >await dv.view("0-辅助/Views/blockPriority", { dv, moment, folder: `3-过程资产/${currentProject}/阻碍`, sprintLength: 1 });
> >```
> 
> > [!warning] 技术债
> > ```dataviewjs
> >await dv.view("0-辅助/Views/techDebtReport", { dv });
> > ```

# 2. 流程改善

| 来源                      | 根因分析                                   | 改善行动                                                                                            | 验收标准                                                                    | 验收结果 |
| ----------------------- | -------------------------------------- | ----------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------- | ---- |
| [[blocker-20250602-01]] | 缺乏结构化的验收标准制定框架与方法论，导致在计划环节耗时过长且产出质量不稳定 | ① 采用临时方案： 执行「负面功能清单+可支撑关键行为」与「渐进式标准构建法」作为新的验收标准制定框架<br>② 实施监控： 启动为期4周的监控期，记录每周因验收标准不清晰导致的“困惑次数” | ① 短期验收： 在4周监控期内，出现连续两周的“每周困惑次数”≤ 1。<br>② 长期验收： 成功将临时方案转化为个人经验方法，并关闭此阻碍。 |      |
# 3. 改善回顾

```dataviewjs
await dv.view("0-辅助/Views/improveSummary", { dv });
```