/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/main.ts
var main_exports = {};
__export(main_exports, {
  default: () => AppendPlugin
});
module.exports = __toCommonJS(main_exports);
var import_obsidian5 = require("obsidian");

// src/config.ts
var import_obsidian4 = require("obsidian");

// src/helper.ts
var import_obsidian = require("obsidian");
var Helper = class {
  // get all folders of this vault.
  // @return ['/', 'folder1/folder2/folder3', 'folder1/folder2/folder4' ...]
  getAllFolder(app) {
    const rootPath = app.vault.getRoot();
    const directories = [];
    const traverse = (folder) => {
      directories.push(folder);
      folder.children.forEach((child) => {
        if (child instanceof import_obsidian.TFolder) {
          traverse(child);
        }
      });
    };
    traverse(rootPath);
    let dirs = [];
    if (directories.length < 1)
      return dirs;
    for (const k in directories) {
      const dir = directories[k];
      dirs.push(dir.path);
    }
    return dirs;
  }
  // add msg to status bar and auto hide
  addStatus(msg, plugin) {
    const statusBarItemEl = plugin.addStatusBarItem();
    statusBarItemEl.setText(msg);
    setTimeout(() => {
      statusBarItemEl.detach();
    }, 3e3);
  }
  // format data string 
  // lang: cn / en  created: unix timestamp in seconds
  formatDate(format, lang = "cn", created = 0) {
    let now;
    if (created < 1) {
      now = new Date();
    } else {
      now = new Date(created * 1e3);
    }
    const tokens = {
      "y": now.getFullYear(),
      // 四位年份
      "m": now.getMonth() + 1,
      // 月份，0 开始所以加 1
      "d": now.getDate(),
      // 日
      "h": now.getHours(),
      // 24 小时制小时
      "i": now.getMinutes(),
      // 分钟
      "s": now.getSeconds(),
      // 秒
      "w": now.getDay()
      // 星期，0是星期日
    };
    const weekdays = {
      cn: ["\u65E5", "\u4E00", "\u4E8C", "\u4E09", "\u56DB", "\u4E94", "\u516D"],
      en: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
    };
    const pad = (value) => value.toString().padStart(2, "0");
    return format.replace(/y|m|d|h|i|s|w|W/g, (match) => {
      if (match === "w") {
        return lang === "cn" ? "\u5468" + weekdays.cn[tokens.w] : weekdays.en[tokens.w].slice(0, 3);
      }
      if (match === "W") {
        return lang === "cn" ? `\u661F\u671F${weekdays.cn[tokens.w]}` : weekdays.en[tokens.w];
      }
      return pad(tokens[match]);
    });
  }
  // if string needed tobe formated
  formatDateInStr(str, createdAt) {
    let lang = this.detectLang();
    return str.replace(/\{([^}]+)\}/g, (match, p1) => this.formatDate(p1, lang, createdAt));
  }
  // detect language, returns  cn or en 
  detectLang() {
    let lang = window.localStorage.getItem("language");
    if (lang == "zh" || lang == "zh-cn" || lang == "zh-TW") {
      return "cn";
    }
    return "en";
  }
  now() {
    return Math.floor(Date.now() / 1e3);
  }
  // get all available templates 
  getAllTemplates(app) {
    const templatePlugin = app.internalPlugins.plugins.templates;
    if (templatePlugin && templatePlugin.instance) {
      const templateFolder = templatePlugin.instance.options.folder;
      const t = app.vault.getFiles().filter(
        (file) => file.path.startsWith(templateFolder + "/")
      );
      return t;
    }
    return [];
  }
};

// src/lang.ts
var Lang = class {
  constructor() {
    this.NAME_APIKEY = "API Key";
    this.DESC_APIKEY = "Get API key from wechatobsidian.com";
    this.PH_APIKEY = "Enter your API key";
    this.MORE_DESC = "For more usage methods, to register a new account, or to provide feedback, please visit:";
    this.NAME_SAVEDIR = "Select folder to save";
    this.DESC_SAVEDIR = "Select which folder to save new messages";
    this.NAME_FILENAME = "File name rule";
    this.DESC_FILENAME = "Rule of added filename when received messages";
    this.FILENAME_RULE_CONTENT = "First line of message";
    this.INSERT_POSITION = "Insert new message at position";
    this.INSERT_POSITION_DESC = "Insert new message at the beginning or the end";
    this.INSERT_POSITION_BEGIN = "Beginning of the file";
    this.INSERT_POSITION_END = "End of the file";
    this.NAME_CONFLICTFILE = "Conflict filename rule";
    this.TITLE_FIXED = "fixed title(supports date variables)";
    this.SET_TITLE_FIXED = "set fixed title, supports timestamp variables, see the instructions below";
    this.TITLE_FIXED_DESC = "Set a fixed file name without needing to include the .md file extension.";
    this.TITLE_FIXED_ERR = "Fixed title error:";
    this.DESC_CONFLICTFILE = "How to deal when filename already existed";
    this.CONFLICTFILE_NEW = "Create new file";
    this.CONFLICTFILE_APPEND = "Append to existed file";
    this.PREFIX_TITLE = "Add prefix for each content";
    this.PREFIX_DESC = "Supports timestamp variables, see the instructions below for details";
    this.SUFFIX_TITLE = "Add suffix for each content";
    this.SUFFIX_DESC = "Supports timestamp variables, see the instructions below for details";
    this.SUFFIX_PREFIX_USAGE = `
Prefix/Suffix/Fixed title configuration usage:
Supports date variables (must be enclosed in curly braces), prefix/suffix supports using \\n for new lines
{y-m-d h:i:s W w} corresponds to year-month-day hour:minute:second Sunday Sun
  For example:
  "date@{y-m-d-W}"
  "{ymd@h:i w} \\n"
  "{y-m-d h:i:s} \\n --- \\n"
`;
    this.NAME_REFRESHINTERVAL = "Refresh new message interval";
    this.DESC_REFRESHINTERVAL = "Refresh new message interval in seconds";
    this.APIKEY_VERIFYOK = "API key verify ok.";
    this.APIKEY_VERIFYERR = "API key verify err:";
    this.NAME_VERIFYBTN = "Verify API key";
    this.DESC_VERIFYBTN = "Check whether API key is valid";
    this.ERROR = "Messager error:";
    this.API_ERROR = "Messager server response error:";
    this.API_USERERR = "API key not found, user not exist.";
    this.CHOOSE_TEMPLATE = "Choose Template(Only first message will use)";
    this.CHOOSE_TEMPLATE_DESC = "Only first message of new file will use";
    this.LATEST_UPDATE = `
UpdateNotes:
    (v1.1.5@25-05-18): Fix Save Email's attach
    (v1.1.4@25-05-15): Save Email's attach
    (v1.1.3@25-05-13): Support template
`;
    let lang = window.localStorage.getItem("language");
    if (lang == "zh" || lang == "zh-cn" || lang == "zh-TW") {
      this.loadChineseLang();
    }
  }
  // load chinese lang 
  loadChineseLang() {
    this.NAME_APIKEY = "API Key";
    this.DESC_APIKEY = "\u524D\u5F80wechatobsidian.com\u83B7\u53D6API key";
    this.PH_APIKEY = "\u8BF7\u8F93\u5165API key";
    this.MORE_DESC = "\u66F4\u591A\u4F7F\u7528\u65B9\u6CD5\uFF0C\u6CE8\u518C\u65B0\u8D26\u6237\uFF0C\u610F\u89C1\u53CD\u9988\u7B49 \u8BF7\u8BBF\u95EE:";
    this.NAME_SAVEDIR = "\u9009\u62E9\u76EE\u5F55";
    this.DESC_SAVEDIR = "\u9009\u62E9\u65B0\u6D88\u606F\u8981\u4FDD\u5B58\u5230\u7684\u76EE\u5F55";
    this.NAME_FILENAME = "\u6587\u4EF6\u540D\u89C4\u5219";
    this.DESC_FILENAME = "\u6536\u5230\u65B0\u6D88\u606F\u65F6\u4FDD\u5B58\u6587\u4EF6\u7684\u540D\u5B57\u89C4\u5219";
    this.FILENAME_RULE_CONTENT = "\u6D88\u606F\u9996\u884C\u5185\u5BB9";
    this.INSERT_POSITION = "\u65B0\u6D88\u606F\u7684\u63D2\u5165\u4F4D\u7F6E";
    this.INSERT_POSITION_DESC = "\u65B0\u6D88\u606F\u63D2\u5165\u5728\u6587\u6863\u5F00\u59CB\u6216\u7ED3\u675F\u4F4D\u7F6E";
    this.INSERT_POSITION_BEGIN = "\u6587\u4EF6\u5934\u90E8";
    this.INSERT_POSITION_END = "\u6587\u4EF6\u5C3E\u90E8";
    this.NAME_CONFLICTFILE = "\u6587\u4EF6\u540D\u5DF2\u5B58\u5728\u65F6\u5904\u7406\u89C4\u5219";
    this.SET_TITLE_FIXED = "\u8BBE\u7F6E\u56FA\u5B9A\u6807\u9898";
    this.TITLE_FIXED_DESC = "\u8BBE\u7F6E\u56FA\u5B9A\u7684\u6587\u4EF6\u540D\uFF0C\u4E0D\u9700\u8981\u5305\u542B .md \u540E\u7F00;\u652F\u6301\u65F6\u95F4\u6233\u53D8\u91CF\uFF0C\u5177\u4F53\u53C2\u8003\u4E0B\u65B9\u8BF4\u660E";
    this.TITLE_FIXED = "\u56FA\u5B9A\u6807\u9898(\u652F\u6301\u65F6\u95F4\u6233\u53D8\u91CF)";
    this.TITLE_FIXED_ERR = "\u56FA\u5B9A\u6807\u9898\u8BBE\u7F6E\u9519\u8BEF\uFF1A";
    this.DESC_CONFLICTFILE = "\u5F53\u6587\u4EF6\u540D\u5DF2\u7ECF\u5B58\u5728\u65F6\u5982\u4F55\u5904\u7406";
    this.CONFLICTFILE_NEW = "\u521B\u5EFA\u65B0\u6587\u4EF6";
    this.CONFLICTFILE_APPEND = "\u5728\u5DF2\u5B58\u5728\u7684\u6587\u4EF6\u540E\u6DFB\u52A0";
    this.PREFIX_TITLE = "\u4E3A\u6BCF\u6761\u5185\u5BB9\u6DFB\u52A0\u524D\u7F00";
    this.PREFIX_DESC = "\u652F\u6301\u65F6\u95F4\u6233\u53D8\u91CF\uFF0C\u5177\u4F53\u53C2\u8003\u4E0B\u65B9\u8BF4\u660E";
    this.SUFFIX_TITLE = "\u4E3A\u6BCF\u6761\u5185\u5BB9\u6DFB\u52A0\u540E\u7F00";
    this.SUFFIX_DESC = "\u652F\u6301\u65F6\u95F4\u6233\u53D8\u91CF\uFF0C\u5177\u4F53\u53C2\u8003\u4E0B\u65B9\u8BF4\u660E";
    this.SUFFIX_PREFIX_USAGE = `\u524D\u7F00/\u540E\u7F00/\u56FA\u5B9A\u6587\u4EF6\u540D \u65F6\u95F4\u6233\u53D8\u91CF\u7528\u6CD5\u8BF4\u660E\uFF1A 
\u65E5\u671F\u53D8\u91CF(\u9700\u8981\u7528\u82B1\u62EC\u53F7\u62EC\u8D77\u6765)\uFF0C\u524D\u7F00/\u540E\u7F00\u652F\u6301\u7528 \\n \u6362\u884C(\u6587\u4EF6\u540D\u4E0D\u652F\u6301)
{y-m-d h:i:s W w} \u5BF9\u5E94 \u5E74-\u6708-\u65E5 \u65F6:\u5206:\u79D2 \u661F\u671F\u516D(\u5927\u5199W) \u5468\u516D(\u5C0F\u5199w)
  \u4F8B\u5982: 
  "\u65E5\u671F@{y-m-d-W}"
  "{ymd@h:i_w} \\n"
  "{y-m-d h:i:s} \\n --- \\n"
`;
    this.NAME_REFRESHINTERVAL = "\u65B0\u6D88\u606F\u5237\u65B0\u95F4\u9694";
    this.DESC_REFRESHINTERVAL = "\u5355\u4F4D\u4E3A\u79D2";
    this.APIKEY_VERIFYOK = "API key \u9A8C\u8BC1\u6210\u529F\uFF01";
    this.APIKEY_VERIFYERR = "API key \u9A8C\u8BC1\u5931\u8D25:";
    this.NAME_VERIFYBTN = "\u68C0\u67E5API key";
    this.DESC_VERIFYBTN = "\u6D4B\u8BD5API key\u662F\u5426\u6B63\u786E";
    this.ERROR = "Messager \u9519\u8BEF:";
    this.API_ERROR = "Messager \u670D\u52A1\u5668\u9519\u8BEF:";
    this.API_USERERR = "\u7528\u6237\u4E0D\u5B58\u5728\u3002";
    this.CHOOSE_TEMPLATE = "\u9009\u62E9\u8981\u4F7F\u7528\u7684\u6A21\u677F";
    this.CHOOSE_TEMPLATE_DESC = "\u540C\u4E00\u4E2A\u6587\u4EF6\u53EA\u6709\u9996\u6761\u4FE1\u606F\u4F1A\u4F7F\u7528\u6A21\u677F";
    this.LATEST_UPDATE = `
\u66F4\u65B0\u8BB0\u5F55\uFF1A
(v1.1.5@25-05-15): Fix Email\u591A\u4E2A\u9644\u4EF6\u5185\u5BB9\u4E0B\u8F7D
(v1.1.4@25-05-15): Email\u9644\u4EF6\u5185\u5BB9\u652F\u6301\u4E0B\u8F7D
(v1.1.3@25-05-13): \u652F\u6301\u6A21\u677F\u6D88\u606F\uFF1B\u652F\u6301Email\u5185\u5D4C\u56FE\u7247\u4E0B\u8F7D
`;
  }
};

// src/note.ts
var import_obsidian3 = require("obsidian");

// src/message.ts
var import_obsidian2 = require("obsidian");
var Message = class {
  constructor() {
    this.lang = new Lang();
    this.apiUrl = "https://wechatobsidian.com/api/get_message";
  }
  // get message from API 
  async getMessage(apikey, verify) {
    try {
      let reqUrl = this.apiUrl + "?apikey=" + apikey;
      if (verify) {
        reqUrl += "&verify=true";
      }
      let resp = await (0, import_obsidian2.requestUrl)(reqUrl);
      if (resp.status != 200) {
        throw Error(this.lang.API_ERROR + " API server status err:" + resp.status);
      }
      if (resp.json.length < 1) {
        throw Error(this.lang.API_ERROR + " API server response empty.");
      }
      let r = resp.json;
      if (typeof r["status"] == "undefined" || r["status"] < 1) {
        throw Error(this.lang.API_ERROR + "resp err.");
      }
      if (r["status"] == 204) {
        throw Error(this.lang.API_USERERR);
      }
      if (r["status"] != 200) {
        if (typeof r["msg"] != "undefined" && r["msg"].length > 0) {
          throw Error(this.lang.API_ERROR + "getMessage:" + r["msg"]);
        } else {
          throw Error(this.lang.API_ERROR + " getMessage status err:" + resp.status);
        }
      }
      if (r["data"].length > 0) {
        new import_obsidian2.Notice("Success get " + r["data"].length + " new messages.");
      }
      return r["data"];
    } catch (err) {
      console.error(this.lang.API_ERROR + " getMessage err,fetch exception", err);
      throw err;
    }
  }
};

// src/note.ts
var Note = class {
  constructor(app, plugin) {
    this.lang = new Lang();
    this.app = app;
    this.plugin = plugin;
    this.helper = new Helper();
  }
  // get message and save to vault
  async getAndSaveMessage(isVerify) {
    try {
      if (this.plugin.settings == null || this.plugin.settings.apikey == null || this.plugin.settings.apikey == "") {
        throw Error(this.lang.PH_APIKEY);
      }
      let note = new Note(this.app, this.plugin);
      let messages = await new Message().getMessage(this.plugin.settings.apikey, isVerify);
      for (let k in messages) {
        let msg = messages[k];
        if (typeof msg == "undefined" || msg.content == null || msg.content.length < 1) {
          console.error("get msg err, empty content.", msg);
          continue;
        }
        let content = msg["content"];
        if (this.judgeImageMessage(content)) {
          content = await this.parseImageOnly(content);
        }
        content = await this.parseEmailContent(content);
        let title = msg["title"];
        if (title != null && title.length > 1) {
          title = this.filterTitle(title) + ".md";
        }
        await note.addNote(this.plugin.settings, content, title, msg["createdAt"]);
      }
    } catch (err) {
      console.error("getAndSaveMessage err:", err);
      throw err;
    }
  }
  // add note to vault
  async addNote(setting, note, title, created) {
    var _a;
    if (title == null || title.length < 1) {
      title = this.getTitle(setting, note, created);
    }
    console.log("\u51C6\u5907addNote", title);
    note = this.dealPrefixOrSuffix(note, created);
    let savedFolder = (_a = setting.savedFolder) != null ? _a : "/";
    let fullpath = "";
    if (savedFolder[savedFolder.length - 1] == "/") {
      if (savedFolder == "/") {
        fullpath = title;
      } else {
        fullpath = savedFolder + title;
      }
    } else if (savedFolder.length < 1) {
      fullpath = title;
    } else {
      fullpath = savedFolder + "/" + title;
    }
    try {
      if (setting.conflictFileRule == null || setting.conflictFileRule == "append" || setting.conflictFileRule.length < 1) {
        if (this.fileExists(fullpath)) {
          let originFile = this.app.vault.getAbstractFileByPath(fullpath);
          if (originFile instanceof import_obsidian3.TFile) {
            let originData = await this.app.vault.read(originFile);
            var newData = "";
            if (setting.insertPosition != null && setting.insertPosition == "beginning") {
              newData = note + "\n" + originData;
            } else {
              newData = originData + "\n" + note;
            }
            await this.app.vault.modify(originFile, newData);
            return;
          } else {
            new import_obsidian3.Notice(this.lang.ERROR + "file:" + fullpath + " not exist with append mode.");
            return;
          }
        } else {
          let newFile = await this.app.vault.create(fullpath, note);
          const leaf = this.app.workspace.getLeaf(true);
          await leaf.openFile(newFile);
          if (this.plugin.settings.templateName != null && this.plugin.settings.templateName.length > 1) {
            await this.insertTemplate();
          }
          return;
        }
      } else {
        let newFile = await this.app.vault.create(fullpath, note);
        const leaf = this.app.workspace.getLeaf(true);
        await leaf.openFile(newFile);
        if (this.plugin.settings.templateName != null && this.plugin.settings.templateName.length > 1) {
          await this.insertTemplate();
        }
      }
      this.helper.addStatus("new message to note:" + fullpath, this.plugin);
    } catch (err) {
      console.error("MessageToObsidian addNote exception:", err);
      new import_obsidian3.Notice(this.lang.ERROR + "file:" + fullpath + " addNote exception:" + err);
    }
  }
  // generate title 
  getTitle(setting, note, created) {
    let title = "";
    let date = new Date();
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    if (setting.filenameRule == "yyyy-mm-dd") {
      title = `${year}-${month}-${day}`;
    }
    if (setting.filenameRule == "mm-dd") {
      title = `${month}-${day}`;
    }
    if (setting.filenameRule == this.lang.FILENAME_RULE_CONTENT) {
      title = note.substr(0, 20);
      let split = note.split("\n");
      if (typeof split[0] != "undefined" && split[0] != null && split[0].length > 0) {
        title = split[0].substr(0, 20);
      }
    }
    if (setting.filenameRule == "fixed" && setting.fixedTitle.length > 0) {
      title = this.helper.formatDateInStr(setting.fixedTitle, created);
    }
    if (title == "") {
      title = `${year}-${month}-${day}`;
    }
    title = this.filterTitle(title);
    if (setting.conflictFileRule != "new") {
      return title + ".md";
    }
    let f = setting.savedFolder + "/" + title + ".md";
    if (!this.fileExists(f)) {
      return title + ".md";
    }
    for (let i = 0; i <= 1e3; i++) {
      let newFile = setting.savedFolder + "/" + title + "(" + i + ").md";
      if (!this.fileExists(newFile)) {
        return title + "(" + i + ").md";
      }
    }
    new import_obsidian3.Notice(this.lang.ERROR + "generate filename err...");
    return Math.random() + ".md";
  }
  // detect if file exists 
  // file MUST NOT start with /, eg: Inbox/22-22.md  22-11.md
  fileExists(file) {
    let f = this.app.vault.getAbstractFileByPath(file);
    if (f == null) {
      return false;
    }
    return f instanceof import_obsidian3.TFile;
  }
  // deal with prefix/suffix of content 
  dealPrefixOrSuffix(note, created) {
    let settings = this.plugin.settings;
    if (settings.contentPrefix != null) {
      if (settings.contentPrefix.length > 0) {
        let prefix = this.helper.formatDateInStr(settings.contentPrefix, created);
        prefix = prefix.replace(/\\n/g, "\n");
        note = prefix + note;
      }
    }
    if (settings.contentSuffix != null) {
      if (settings.contentSuffix.length > 0) {
        let suffix = this.helper.formatDateInStr(settings.contentSuffix, created);
        suffix = suffix.replace(/\\n/g, "\n");
        note = note + suffix;
      }
    }
    return note;
  }
  // filter title special char 
  filterTitle(title) {
    if (title.length < 1) {
      return "";
    }
    const validChars = title.match(/[a-zA-Z0-9\u4e00-\u9fa5+-_.@｜]+/g);
    if (!validChars) {
      return "undefined";
    }
    let newTitle = validChars.join("");
    return newTitle.replace(/[/\\^:]/g, "");
  }
  // judge if message is image only
  // @return true if message is image only
  judgeImageMessage(msg) {
    const fullStringRegex = /^\!\[.*?\]\((.+?)\)$/;
    const imageSuffix = ["jpg", "jpeg", "png", "gif"];
    if (!fullStringRegex.test(msg)) {
      return false;
    }
    const urlRegex = /\]\((https?:\/\/[^\s)]+)\)/;
    const match = msg.match(urlRegex);
    if (match == null || match.length < 2) {
      return false;
    }
    let url = match[1];
    if (url.indexOf("qpic.cn") > 0) {
      return true;
    }
    let suffix = url.substr(-3, 3);
    if (imageSuffix.indexOf(suffix) >= 0) {
      return true;
    }
    suffix = url.substr(-4, 4);
    if (imageSuffix.indexOf(suffix) >= 0) {
      return true;
    }
    return false;
  }
  // parse imageOnly message and save image to local 
  // @return localfile note, eg:  ![[somgpic.png]]
  async parseImageOnly(msg) {
    const urlRegex = /\]\((https?:\/\/[^\s)]+)\)/;
    const match = msg.match(urlRegex);
    if (match == null || match.length < 2) {
      return msg;
    }
    let url = match[1];
    let localPath = await this.saveUrlToLocal(url);
    if (localPath != "") {
      let localMsg = "![[" + localPath + "|400]]";
      return localMsg;
    }
    return "";
  }
  // save image's url to local
  // return local path, eg: localFolder/media/somgPic.jpg
  async saveUrlToLocal(url) {
    let settings = this.plugin.settings;
    let resp = await (0, import_obsidian3.requestUrl)(url);
    if (resp.status != 200 || resp.arrayBuffer.byteLength < 1) {
      console.error("saveUrlToLocal err,url:", url, "resp err:", resp);
      return "";
    }
    let imgData = new Uint8Array(resp.arrayBuffer);
    let imgInfo = this.checkImageExistence(await this.getImageSavedPath(settings, url));
    let imgPath = imgInfo[0];
    let imgName = imgInfo[1];
    if (imgPath == null || imgPath == "" || imgName == null || imgName == "") {
      console.error("saveUrlToLocal err, imgPath or imgName empty:", imgPath, imgName);
      return "";
    }
    try {
      const file = await this.app.vault.createBinary(imgPath, imgData);
      if (file == null || typeof file.basename == "undefined" || file.basename.length < 1) {
        console.error("saveUrlToLocal vault.createBinary err,file not saved.");
        return "";
      }
      return imgPath;
    } catch (err) {
      console.error("saveUrlToLocal createBinary err:", err);
      return "";
    }
  }
  // get image saved path
  // @return  [pathWithImgName: var/to/folder/a.jpg or "a.jpg", fileName]
  async getImageSavedPath(setting, url) {
    var _a;
    let fileName = "";
    let urlObj = new URL(url);
    let urlPath = urlObj.pathname;
    if (urlPath == null || urlPath.length < 1) {
      fileName = this.helper.now().toString() + ".jpg";
    } else {
      fileName = this.helper.now().toString() + urlPath.substring(urlPath.lastIndexOf("/") + 1);
    }
    if (fileName.indexOf(".") < 0) {
      fileName = fileName + ".jpg";
    }
    let pluginPath = (_a = setting.savedFolder) != null ? _a : "";
    if (pluginPath == "/") {
      pluginPath = "";
    }
    let systemPath = this.app.vault.getConfig("attachmentFolderPath");
    if (systemPath == null || systemPath.length < 1) {
      return [pluginPath + "/" + fileName, fileName];
    }
    if (systemPath == "./") {
      if (pluginPath == "") {
        return [fileName, fileName];
      } else {
        return [pluginPath + "/" + fileName, fileName];
      }
    }
    if (systemPath == "/") {
      return [fileName, fileName];
    }
    if (systemPath.length > 2 && systemPath.substr(0, 2) == "./") {
      systemPath = systemPath.substring(2);
      if (pluginPath == "") {
        await this.checkAndCreateFolder(systemPath);
        return [systemPath + "/" + fileName, fileName];
      } else {
        await this.checkAndCreateFolder(pluginPath + "/" + systemPath);
        return [pluginPath + "/" + systemPath + "/" + fileName, fileName];
      }
    } else {
      return [systemPath + "/" + fileName, fileName];
    }
  }
  // get a new name for image file if already exists 
  checkImageExistence(info) {
    if (info.length != 2) {
      return ["", ""];
    }
    let path = info[0];
    let fileName = info[1];
    if (!this.fileExists(path)) {
      return [path, fileName];
    }
    let newFile = this.helper.now().toString() + fileName;
    let newPath = path.replace(fileName, newFile);
    return [newPath, newFile];
  }
  // create folder if not exist 
  async checkAndCreateFolder(folder) {
    let exist = await this.app.vault.adapter.exists(folder);
    if (exist) {
      return;
    }
    await this.app.vault.createFolder(folder);
  }
  // insert template to current opening file
  async insertTemplate() {
    var _a, _b, _c;
    let configTmpName = this.plugin.settings.templateName;
    if (configTmpName.length < 1) {
      console.error("no configTmpName err.");
      return;
    }
    const templatePlugin = (_c = (_b = (_a = this.app.internalPlugins) == null ? void 0 : _a.plugins) == null ? void 0 : _b.templates) == null ? void 0 : _c.instance;
    if (!templatePlugin) {
      console.log("template plugin not available.");
      return;
    }
    const templates = this.helper.getAllTemplates(this.app);
    let tmpFile = null;
    for (let k in templates) {
      if (templates[k].name == configTmpName) {
        tmpFile = templates[k];
      }
    }
    if (tmpFile == null || tmpFile.path == null || tmpFile.path.length < 1) {
      console.error("parse template err, template may not exist,path empty.");
      return;
    }
    const templateContent = await this.app.vault.read(tmpFile);
    if (templateContent.length < 1) {
      console.error("template content empty.", tmpFile.path);
      return;
    }
    await templatePlugin.insertTemplate(tmpFile);
  }
  // parse email's content, save embended file && attach file to local
  async parseEmailContent(content) {
    console.log("\u5F00\u59CB\u89E3\u6790email", content);
    let emailAttachApi = "https://wechatobsidian.com/api/email_attach";
    let settings = this.plugin.settings;
    let apiKey = settings.apikey;
    let matches = [];
    let match;
    const regex = /<img[^>]+src=\"(.*?)\"/gi;
    while ((match = regex.exec(content)) !== null) {
      matches.push(match[1]);
    }
    console.log("\u5339\u914D\u4E86matches", matches);
    for (let k in matches) {
      let url = matches[k];
      if (url.substring(0, 8) == "https://") {
        let localPath = await this.saveUrlToLocal(url);
        if (localPath.length > 0) {
          content = content.replace(url, localPath);
        }
      } else if (url.substring(0, 4) == "cid:") {
        let filename = url.substring(4);
        let req = emailAttachApi + "?apikey=" + apiKey + "&filename=" + filename;
        let localPath = await this.saveUrlToLocal(req);
        if (localPath.length > 0) {
          content = content.replace(url, localPath);
        }
      }
    }
    const attRegex = /!\[\[#Attachment#([^\]]+)\]\]/g;
    while ((match = attRegex.exec(content)) !== null) {
      if (match.length != 2) {
        console.error("attachment regex match result not 2");
        continue;
      }
      let req = emailAttachApi + "?apikey=" + apiKey + "&filename=" + match[1];
      let localPath = await this.saveUrlToLocal(req);
      if (localPath.length < 1) {
        console.error("save attachment:" + match[1] + "-failed.." + req);
        continue;
      }
      let replaced = "![[" + localPath + "|400]]";
      content = content.replace(match[0], replaced);
    }
    return content;
  }
};

// src/config.ts
var AppendSettingTab = class extends import_obsidian4.PluginSettingTab {
  // container for fixed title input
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
    this.app = app;
    this.helper = new Helper();
    this.lang = new Lang();
  }
  display() {
    const { containerEl } = this;
    containerEl.empty();
    new import_obsidian4.Setting(containerEl).setName(this.lang.NAME_APIKEY).setDesc(this.lang.DESC_APIKEY).addText(
      (text) => {
        var _a;
        return text.setPlaceholder(this.lang.PH_APIKEY).setValue((_a = this.plugin.settings.apikey) != null ? _a : "").onChange(async (value) => {
          this.plugin.settings.apikey = value;
          await this.plugin.saveSettings();
        });
      }
    );
    const allFolders = this.helper.getAllFolder(this.app);
    new import_obsidian4.Setting(containerEl).setName(this.lang.NAME_SAVEDIR).setDesc(this.lang.DESC_SAVEDIR).addDropdown((dropdown) => {
      for (const k in allFolders) {
        const f = allFolders[k];
        dropdown.addOption(f, f);
      }
      if (this.plugin.settings.savedFolder.length < 1) {
        dropdown.setValue("/");
      } else {
        dropdown.setValue(this.plugin.settings.savedFolder);
      }
      dropdown.onChange(async (value) => {
        this.plugin.settings.savedFolder = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian4.Setting(containerEl).setName(this.lang.NAME_FILENAME).setDesc(this.lang.DESC_FILENAME).addDropdown((dropdown) => {
      dropdown.addOption("yyyy-mm-dd", "yyyy-mm-dd");
      dropdown.addOption("mm-dd", "mm-dd");
      dropdown.addOption(this.lang.FILENAME_RULE_CONTENT, this.lang.FILENAME_RULE_CONTENT);
      dropdown.addOption("fixed", this.lang.TITLE_FIXED);
      if (this.plugin.settings.filenameRule.length < 1) {
        dropdown.setValue("mm-dd");
      } else {
        dropdown.setValue(this.plugin.settings.filenameRule);
      }
      dropdown.onChange(async (value) => {
        this.plugin.settings.filenameRule = value;
        if (value == "fixed") {
          this.inputForFixedTitle();
        } else {
          this.fixedTitleContainer.empty();
          this.plugin.settings.fixedTitle = "";
          await this.plugin.saveSettings();
        }
      });
      this.fixedTitleContainer = containerEl.createDiv();
      if (this.plugin.settings.filenameRule == "fixed") {
        this.inputForFixedTitle();
      }
    });
    new import_obsidian4.Setting(containerEl).setName(this.lang.NAME_CONFLICTFILE).setDesc(this.lang.DESC_CONFLICTFILE).addDropdown((dropdown) => {
      dropdown.addOption("new", this.lang.CONFLICTFILE_NEW);
      dropdown.addOption("append", this.lang.CONFLICTFILE_APPEND);
      if (this.plugin.settings.conflictFileRule.length < 1) {
        dropdown.setValue("append");
      } else {
        dropdown.setValue(this.plugin.settings.conflictFileRule);
      }
      dropdown.onChange(async (value) => {
        this.plugin.settings.conflictFileRule = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian4.Setting(containerEl).setName(this.lang.PREFIX_TITLE).setDesc(this.lang.PREFIX_DESC).addText(
      (text) => {
        var _a;
        return text.setPlaceholder(this.lang.PREFIX_TITLE).setValue((_a = this.plugin.settings.contentPrefix) != null ? _a : "").onChange(async (value) => {
          this.plugin.settings.contentPrefix = value;
          await this.plugin.saveSettings();
        });
      }
    );
    new import_obsidian4.Setting(containerEl).setName(this.lang.SUFFIX_TITLE).setDesc(this.lang.SUFFIX_DESC).addText(
      (text) => {
        var _a;
        return text.setPlaceholder(this.lang.SUFFIX_TITLE).setValue((_a = this.plugin.settings.contentSuffix) != null ? _a : "").onChange(async (value) => {
          this.plugin.settings.contentSuffix = value;
          await this.plugin.saveSettings();
        });
      }
    );
    new import_obsidian4.Setting(containerEl).setName(this.lang.INSERT_POSITION).setDesc(this.lang.INSERT_POSITION_DESC).addDropdown((dropdown) => {
      dropdown.addOption("beginning", this.lang.INSERT_POSITION_BEGIN);
      dropdown.addOption("ending", this.lang.INSERT_POSITION_END);
      if (this.plugin.settings.insertPosition == null) {
        dropdown.setValue("ending");
      } else {
        dropdown.setValue(this.plugin.settings.insertPosition);
      }
      dropdown.onChange(async (value) => {
        this.plugin.settings.insertPosition = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian4.Setting(containerEl).setName(this.lang.NAME_REFRESHINTERVAL).setDesc(this.lang.DESC_REFRESHINTERVAL).addDropdown((dropdown) => {
      dropdown.addOption("10", "10");
      dropdown.addOption("30", "30");
      dropdown.addOption("60", "60");
      dropdown.addOption("180", "180");
      dropdown.addOption("300", "300");
      if (this.plugin.settings.refreshInterval.length < 1) {
        dropdown.setValue("30");
      } else {
        dropdown.setValue(this.plugin.settings.refreshInterval);
      }
      dropdown.onChange(async (value) => {
        this.plugin.settings.refreshInterval = value;
        await this.plugin.saveSettings();
      });
    });
    const templates = this.helper.getAllTemplates(this.app);
    new import_obsidian4.Setting(containerEl).setName(this.lang.CHOOSE_TEMPLATE).setDesc(this.lang.CHOOSE_TEMPLATE_DESC).addDropdown((dropdown) => {
      dropdown.addOption("", "");
      for (const k in templates) {
        let option = templates[k];
        dropdown.addOption(option.name, option.name);
      }
      dropdown.setValue(this.plugin.settings.templateName);
      dropdown.onChange(async (value) => {
        this.plugin.settings.templateName = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian4.Setting(containerEl).setName(this.lang.NAME_VERIFYBTN).setDesc(this.lang.DESC_VERIFYBTN).addButton((button) => {
      button.setButtonText(this.lang.NAME_VERIFYBTN).setCta().onClick(async () => {
        await this.plugin.saveSettings();
        try {
          let note = new Note(this.app, this.plugin);
          await note.getAndSaveMessage(true);
        } catch (err) {
          new import_obsidian4.Notice(this.lang.APIKEY_VERIFYERR + err);
          return;
        }
      });
    });
    const p = containerEl.createEl("p");
    p.appendText(this.lang.MORE_DESC);
    p.createEl("a", {
      text: "Here",
      href: "https://wechatobsidian.com/"
    });
    p.style.fontSize = "12px";
    p.style.color = "#888888";
    containerEl.createEl("hr");
    const usage = containerEl.createEl("pre");
    usage.appendText(this.lang.SUFFIX_PREFIX_USAGE);
    usage.style.fontSize = "12px";
    usage.style.color = "#888888";
    containerEl.createEl("hr");
    const updDesc = containerEl.createEl("pre");
    updDesc.appendText(this.lang.LATEST_UPDATE.replace(/\\n/g, "\n"));
    updDesc.style.fontSize = "12px";
    updDesc.style.color = "#888888";
  }
  // add an input setting for set fixed title 
  inputForFixedTitle() {
    this.fixedTitleContainer.empty();
    new import_obsidian4.Setting(this.fixedTitleContainer).setName(this.lang.SET_TITLE_FIXED).setDesc(this.lang.TITLE_FIXED_DESC).addText(
      (text) => {
        var _a;
        return text.setPlaceholder(this.lang.SET_TITLE_FIXED).setValue((_a = this.plugin.settings.fixedTitle) != null ? _a : "").onChange(async (value) => {
          if (value.indexOf(".") >= 0) {
            new import_obsidian4.Notice(this.lang.TITLE_FIXED_ERR + "can't include . in filename");
            return;
          }
          this.plugin.settings.fixedTitle = value;
          await this.plugin.saveSettings();
        });
      }
    );
  }
};

// src/main.ts
var AppendPlugin = class extends import_obsidian5.Plugin {
  async onload() {
    await this.loadSettings();
    this.addSettingTab(new AppendSettingTab(this.app, this));
    if (true) {
      this.intervalRefresh();
    } else {
      setTimeout(() => {
        this.intervalRefresh();
      }, 3e4);
    }
  }
  onunload() {
  }
  async loadSettings() {
    let oriData = await this.loadData();
    if (oriData != null && typeof oriData.apikey != "undefined" && oriData.apikey.length > 1) {
      this.settings = Object.assign({}, oriData);
    } else {
      const defaultConf = {
        apikey: "",
        savedFolder: "",
        filenameRule: "",
        conflictFileRule: "",
        refreshInterval: "",
        fixedTitle: "",
        insertPosition: "",
        contentSuffix: "",
        contentPrefix: "",
        templateName: ""
      };
      this.settings = defaultConf;
    }
    return this.settings;
  }
  async saveSettings() {
    await this.saveData(this.settings);
  }
  // interval refresh 
  intervalRefresh() {
    let interval = 10;
    if (this.settings != null && typeof this.settings.refreshInterval != "undefined" && this.settings.refreshInterval != null) {
      if (Number(this.settings.refreshInterval) > 1) {
        interval = Number(this.settings.refreshInterval) * 1e3;
      } else {
        interval = 10 * 1e3;
      }
    }
    this.registerInterval(window.setInterval(async () => {
      try {
        let note = new Note(this.app, this);
        await note.getAndSaveMessage(false);
      } catch (err) {
        console.error("Messager plugin err:", err);
        sleep(interval);
      }
    }, interval));
  }
};

/* nosourcemap */