/* 单元格垂直居中 - 原有代码 */
body {
  --table-cell-vertical-alignment: middle;
}
.table-cell-wrapper { height: unset !important; }

/* 1. 表头行居中（排除交叉单元格） */
tr:first-child > :not(:first-child),
thead tr th:not(:first-child) {
  text-align: center !important;
}

/* 2. 首列居中（排除表头） */
tbody tr > :first-child {
  text-align: center !important;
}

/* 3. 交叉单元格水平垂直居中 */
tr:first-child > :first-child,
thead tr th:first-child {
  text-align: center !important;
  vertical-align: middle !important;
}

/* 4. 精确修复第二行问题 */
tbody tr:first-child > :not(:first-child) {
  text-align: left !important;
}

/* 完全删除表格前的空行 */
.is-live-preview :is([class=cm-line]:has(+ .cm-table-widget)) {
    display: none !important;             /* 隐藏空行 */
    height: 0 !important;                 /* 高度设为0 */
    padding: 0 !important;                /* 移除内边距 */
    margin: 0 !important;                 /* 移除外边距 */
    opacity: 0 !important;                /* 完全透明 */
    pointer-events: none !important;      /* 禁用交互 */
    overflow: hidden !important;          /* 隐藏溢出内容 */
    border: none !important;              /* 移除边框 */
    line-height: 0 !important;            /* 行高设为0 */
    font-size: 0 !important;              /* 字体大小设为0 */
    min-height: 0 !important;             /* 最小高度设为0 */
    max-height: 0 !important;             /* 最大高度设为0 */
}