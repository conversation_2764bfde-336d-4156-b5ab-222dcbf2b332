---
homePageLink: "[[PKMS-首页]]"
---
# 1. 本周方向
```dataviewjs
	// 获取当前文件的路径信息，提取项目名称
	const currentFile = dv.current().file;
	const currentPath = currentFile.path;
	const projectName = currentPath.split("/")[1].trim();
	
	//获取周数、年
	const weekNumber = Number(dv.current().file.name.split("WK")[1]) -1;
	const year = dv.current().file.name.split("-")[1];
	
	//动态生成文件名
	const dynamicFilename = `Replay-${year}-WK${weekNumber.toString().padStart(2, '0')}.md`;
	const path = `2-项目/${projectName}/3-每周复盘/${dynamicFilename}`;
	const file = app.vault.getAbstractFileByPath(path);
	const targetHeading = "# 6. 下周聚焦";
	if(file){
		//读取并解析文件
		const content = await dv.io.load(path);
		const headingRegex = new RegExp(`(${targetHeading}[^]*?\\n)([^]*?)(?=\\n#|$)`);
		const match = content.match(headingRegex);
		const result = match[2].trim();
		dv.paragraph(result)
	}else{
		dv.el("p","无内容")
	}
```
# 2.  [[字段提示信息表#周目标描述： f37322|周目标]] 

- 利用[仓储知识引擎]项目完成PKMS项目工作流验证（[KR1]）
- 完成PKMS知识模块基础组件搭建（[KR1]）
- 完成PKMS项目组件数据查询列表代码编写（[KR1]）



# 3. [[字段提示信息表#验收标准描述 19887e|验收标准]] 

- [x] [仓储知识引擎]项目可以单独在各个项目组件中流畅运行（排除自动化部分）
- [x] [仓储知识引擎]项目可以在PKMS项目工作流（[首页]-->[周循环计划]-->[每日执行]-->[每周复盘]-->[每周回顾]）中顺畅运行（排除自动化部分）
- [x] 完成PKMS项目组件查询代码编写，并在每个组件中可用
- [x] 在obsidian中完成PKMS知识模块组件的初步搭建，形成对应的模板



# 4. 关联任务

- [x] 测试[仓储知识引擎]项目在[首页]中运行情况
- [x] 测试[仓储知识引擎]项目在[周循环计划]中运行情况
- [x] 测试[仓储知识引擎]项目在[每日执行]中运行情况
- [x] 测试[仓储知识引擎]项目在[每周复盘]中运行情况
- [x] 测试[仓储知识引擎]项目在[每周回顾]中运行情况
- [x] 测试[仓储知识引擎]项目是否可以在整个项目工作流中顺畅运行
- [x] 尝试设计PKMS知识模块组件的对应模板（不含知识转化监控）
- [x] 在OB中尝试设计PKMS知识模对应的文件树结构
- [x] 为[每日执行]、[每周复盘]组件添加灵感记录模块（不含自动化查询及代码编写）
- [x] 利用[仓储知识引擎]项目成果[PKMS价值流程图]验证知识模块的工作流（不含知识转化监控） ⏳ 2025-06-06
- [x] 编写PKMS[项目首页]组件数据查询代码 ⏳ 2025-06-08
- [x] 编写PKMS[每周规划]组件数据查询代码
- [x] 编写PKMS[每日执行]组件数据查询代码 ⏳ 2025-06-08
- [x] 编写PKMS[每周复盘]组件数据查询代码 ⏳ 2025-06-08
- [x] 编写PKMS[每周回顾]组件数据查询代码 ⏳ 2025-06-08