function blockPriority({ dv, moment, folder, sprintLength = 1 }) {
  const pages = dv
    .pages(`"${folder}"`)
    .filter((p) => p.status === "进行中" && p.file.name !== "dashboard")
    .sort((p) => p.createdDate, "asc");

  const parseType = (t) => {
    const s = String(t).trim();
    if (/Replay-\d{4}-WK\d{2}/i.test(s)) return "weeklyReviews";
    if (/Plan-\d{4}-WK\d{2}/i.test(s)) return "weeklyPlans";
    if (/blocker-\d{8}-\d{2}/i.test(s)) return "blockages";
    if (/Retro-\d{4}-WK\d{2}/i.test(s)) return "weeklyRetros";
    return "tasks";
  };

  const m = (d) =>
    moment(
      String(d).replace(/(\+\d{2}):(\d{2})$/, "$1$2"),
      ["YYYY-MM-DDTHH:mm:ss.SSSZZ", "YYYY-MM-DD"],
      true
    );

  const age = (d) => {
    const now = moment().isoWeek(),
      cre = m(d).isoWeek();
    return now >= cre
      ? now - cre + 1
      : moment(`${m(d).year()}-12-28`).isoWeeksInYear() - cre + now + 1;
  };

  pages.forEach((p) => {
    const rel = {
      tasks: [],
      blockages: [],
      weeklyPlans: [],
      weeklyReviews: [],
      weeklyRetros: [],
    };
    (Array.isArray(p.location) ? p.location : [p.location])
      .filter(Boolean)
      .forEach((l) => (rel[parseType(l)] || rel.tasks).push(l));

    const typeW = { 阻塞型: 1.3, 价值威胁: 1, 复合型: 1.6 }[p.type] || 1;
    const ageIt = age(p.createdDate);
    const eFreq = Object.entries(rel).reduce((sum, [k, arr]) => {
      const w =
        {
          tasks: 1.2,
          blockages: 1.5,
          weeklyPlans: 2,
          weeklyReviews: 2.5,
          weeklyRetros: 2.2,
        }[k] || 1;
      return (
        sum +
        Math.pow(
          w,
          Math.min(3, Object.values(rel).filter((v) => v.length).length)
        ) *
          arr.length *
          sprintLength
      );
    }, 0);
    let scene = 0.1;
    const mt = new Set();
    ["weeklyPlans", "weeklyReviews", "weeklyRetros"].forEach((t) => {
      if (rel[t].length) {
        scene += { weeklyPlans: 0.15, weeklyReviews: 0.3, weeklyRetros: 0.25 }[
          t
        ];
        mt.add(t);
      }
    });
    if (mt.size > 1) scene += 0.15 * (mt.size - 1);
    scene += 0.1 * Math.max(0, Object.values(rel).flat().length - 1);
    scene = Math.min(0.85, scene);

    p._prio =
      typeW *
      (1 + Math.sqrt((ageIt * sprintLength) / 16)) *
      Math.log2(eFreq + 1.2) *
      (1 + scene);
  });

  const tops = pages.sort((p) => p._prio, "desc").slice(0, 3);
  if (!tops.length) return dv.paragraph("没有未解决的阻碍");
  dv.el("p", `TOP ${tops.length} 高优先级阻碍`);
  dv.paragraph(tops.map((p) => `[[${p.file.path}|${p.aliases}]]`));
}
blockPriority(input);
