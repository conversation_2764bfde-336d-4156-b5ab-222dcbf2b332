# 1.  成果验收
> [!dashboard]
> 
> > [!todo] 成果统计
> >```dataviewjs
> >await dv.view("0-辅助/Views/weeklyOutputSummary", { dv });
> >```
> 
> > [!tip] 任务统计
> >```dataviewjs
> >await dv.view("0-辅助/Views/weeklyTaskStats", { dv });
> >```
# 2. KR进度

| 序号  | 成果（✅） | `$= "[[2-项目/" + dv.current().file.path.split("/")[1] + "/" +dv.current().file.path.split("/")[1] + "-首页#1. OKR设定" + "\|关联KR]]"` | 价值描述 | 风险预警 | 下一行动 |
| --- | :---- | ------------------------------------------------------------------------------------------------------------------------------- | ---- | ---- | ---- |
|     |       |                                                                                                                                 |      |      |      |

# 3. 交付异常

| 序号  | 成果/任务（🌗❌） | 关键进展 | 关联障碍/原因 | 根因分析 | 下一步行动 |
| --- | ---------- | ---- | ------- | ---- | ----- |
|     |            |      |         |      |       |

