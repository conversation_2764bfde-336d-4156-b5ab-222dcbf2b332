---
startDate: ""
endDate: ""
---
# 1. OKR设定

- O：
- KR1：
- KR2：

# 2. 项目进展

```dataviewjs
await dv.view("0-辅助/Views/weeklyKrProgress", { dv });
```
# 3. 交付异常

```dataviewjs
await dv.view("0-辅助/Views/deliveryAnomalies", { dv });
```
# 4. 变更控制

| 变更日期 | 变更内容 | 变更类型 | 原决策依据 | 失误分析 | 关键证据 | 新行动计划 |
| ---- | ---- | ---- | ----- | ---- | ---- | ----- |
|      |      |      |       |      |      |       |

