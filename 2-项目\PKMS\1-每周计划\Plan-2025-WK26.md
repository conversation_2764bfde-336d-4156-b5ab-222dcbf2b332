---
homePageLink: "[[PKMS-首页]]"
上周复盘: "[[Review-2025-WK24]]"
---
# 1. [[字段提示信息表#周目标描述： f37322|周目标]] 

- 优化后「每周复盘」、「每周回顾」模板使其支持「零阻力复盘流程」（连续、自然地完成复盘全流程，无需因模板设计问题中断思考或补充信息）


# 2. [[字段提示信息表#验收标准描述 19887e|验收标准]] 

| 序号  | 交付物          | 标准/通过条件                                                                                                                                                                                                                                       | 备注  |
| --- | ------------ | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --- |
| 1   | 「每周复盘」模板V1.0 | **1、在使用该模板进行本周复盘时，未出现以下任一情况：**<br>（1）「不知道该填写什么/下一步做什么」导致流程中断<br>（2）出现3次以上「这个信息该填在哪里？」的疑问<br>（3）选择主动跳过模板中某个模块（因认为「无用」或「太复杂」）<br>**2、模板需包含“执行偏差”管理，且必须实现以下核心功能：**<br>（1）能系统化暴露计划与实际的显著差异（至少识别出1项未完成/超预期任务）<br>（2）能引导用户完成偏差根因定位<br>（3）输出可行动的改进线索 |     |
| 2   | 「每周回顾」模板V1.0 | **1、在使用该模板进行本周复盘时，未出现以下任一情况：**<br>（1）「不知道该填写什么/下一步做什么」导致流程中断<br>（2）出现3次以上「这个信息该填在哪里？」的疑问<br>（3）选择主动跳过模板中某个模块（因认为「无用」或「太复杂」）<br>**2、模板需包含“执行偏差”管理，且必须实现以下核心功能：**<br>（1）能系统化暴露计划与实际的显著差异（至少识别出1项未完成/超预期任务）<br>（2）能引导用户完成偏差根因定位<br>（3）输出可行动的改进线索 |     |
# 3. 关联任务

- [x] 探索「每周复盘」组件的内容结构，并寻找可行性方案 ⏳ 2025-06-23 ✅ 2025-06-24
- [x] 尝试修改本周「Replay-2025-WK26」文件内容结构并验证其可行性 ⏳ 2025-06-23 ✅ 2025-06-24
- [x] 探索「每周回顾」组件的内容结构，并寻找可行性方案 ⏳ 2025-06-25 ✅ 2025-06-28
- [x] 尝试修改本周「Review-2025-WK26」文件内容结构并验证其可行性 ⏳ 2025-06-25 ✅ 2025-06-28
- [x] 验证「每周复盘」+「每周回顾」在整个项目工作流程中的流畅性 ⏳ 2025-06-28 ✅ 2025-06-28
- [x] 探索“执行偏差”模块的内容结构，性寻找可行性方案 ⏳ 2025-06-28 ✅ 2025-06-28
- [x] 尝试修改本周「Replay-2025-WK26」文件内容结构（“执行偏差”模块）并验证其可行性 ⏳ 2025-06-28 ✅ 2025-06-28
- [x] 尝试修改本周「Review-2025-WK26」文件内容结构（“执行偏差”模块）并验证其可行性 ⏳ 2025-06-28 ✅ 2025-06-28
- [x] 验证“执行偏差”模块在「Replay-2025-WK26」和「Review-2025-WK26」之间运行的流畅性 ⏳ 2025-06-28 ✅ 2025-06-28
- [x] 重新优化【每周复盘】、【每周回顾】模板 ⏳ 2025-06-28 ✅ 2025-06-28