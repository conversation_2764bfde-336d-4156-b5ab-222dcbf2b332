---
createdDate:
lastUpdate:
版本号:
---
**1. 目标**

- 需解决的问题/实现的成果

**2. 适用场景**

- 何时使用/何时禁用?
- 限制条件？

**3. 关键流程图**



**4. 前置条件**

- 执行前需准备什么？
- [ ] 准备校准工具包  
- [ ] 获取用户画像草案  
- [ ] 操作类SOP关联：[SOP-PREP_v1]


**5. 输出/交付物**

- 执行后产生什么？


**6. 方法步骤**

- 步骤1
	- 动作描述：具体做什么？（认知逻辑--解决问题的思考框架，传递决策逻辑，无标准解，路径多变）
	- 执行标准：如何判断做对了？
	- 工具/资源：模板/软件/数据源/[关联动作对应的SOP（可选）]（例：附访谈问题模板链接）
	- 坑点：需避免的坑（例：避免引导性提问如“你是否觉得XX功能不好用？”）  
- 步骤2
	- ……

**7. 风险控制**

- 高频失效场景
- 应急方案（[关联应急SOP（可选）]）

**8. 修订历史**


| 序号  | 修订日期       | 版本号  | 关联SOP              | 修订内容摘要    |
| --- | ---------- | ---- | ------------------ | --------- |
| 1   | 2025-06-20 | v1.2 | [SOP-ENGINE_v3]（因） | 优化聚类算法（果） |
