// 测试转义竖线的替换逻辑

const templateContent = `| 序号  | 成果（✅） | 关联KR | 价值描述 | 风险预警 | 调整建议 |
| --- | :---- | ---- | ---- | ---- | ---- |
| 1   |       |      |      |      |      |`;

const projectName = 'PKMS';

console.log('=== 原始内容 ===');
console.log(templateContent);

console.log('\n=== 使用转义竖线替换 ===');

// 使用转义的竖线
let modifiedContent = templateContent.replace(
  /关联KR/g,
  `[[${projectName}-首页#1. OKR设定\\|关联KR]]`
);

console.log('替换后的内容:');
console.log(modifiedContent);

console.log('\n=== 检查表格结构 ===');
const lines = modifiedContent.split('\n');
lines.forEach((line, index) => {
  if (line.includes('关联KR')) {
    console.log(`行 ${index + 1}: ${line}`);
    const parts = line.split('|');
    console.log(`  列数: ${parts.length}`);
    console.log(`  各列:`, parts.map(p => `"${p.trim()}"`));
  }
});

console.log('\n=== 链接格式检查 ===');
const linkLine = lines.find(line => line.includes('关联KR'));
if (linkLine) {
  console.log('链接文本:', linkLine);
  // 检查Obsidian是否能识别这个格式
  const hasProperLink = linkLine.includes('[[PKMS-首页#1. OKR设定\\|关联KR]]');
  console.log('链接格式正确:', hasProperLink);
}