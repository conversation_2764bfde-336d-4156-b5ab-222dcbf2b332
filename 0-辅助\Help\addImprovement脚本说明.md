# addImprovement 脚本使用说明

## 功能概述

addImprovement 脚本提供快速记录和访问项目改进相关内容的自动化能力，支持三种操作模式：

| 操作类型    | 功能描述                 | 目标位置                        |
| ------- | -------------------- | --------------------------- |
| 记录洞见/发现 | 将灵感快速记录到改进待办事项的临时记录区 | `2-项目/{项目名}/改进待办事项.md`      |
| 记录知识/信息 | 打开效率工具箱文件供编辑         | `3-过程资产/{项目名}/@其他/效率工具箱.md` |
| 打开改进文档  | 直接访问改进待办事项文件         | `2-项目/{项目名}/改进待办事项.md`      |

## 文件结构

```
0-辅助/
├── Scripts/
│   ├── addImprovement.js          # 核心脚本实现
│   └── addInsight脚本说明.md      # 本文档
└── Templater/
    └── Function/
        └── addImprovement.md      # Templater调用接口
```

## 快速开始

### 1. 快捷键配置

**操作路径**：Obsidian设置 → 快捷键 → 搜索"addImprovement"

**推荐快捷键**：`Ctrl + Shift + I`（避免与其他功能冲突）

### 2. 使用流程

```
快捷键触发 → 选择操作类型 → 内容输入/文件打开 → 自动定位目标文件
```

**步骤说明**：
1. 在项目文件中按下快捷键
2. 选择操作类型（1/2/3）
3. 根据选择完成相应操作
4. 脚本自动处理文件定位和内容添加

## 功能详解

### 2.1 记录洞见/发现

**触发条件**：选择【1】记录洞见/发现

**处理逻辑**：
- 弹窗输入洞见内容
- 自动定位改进待办事项文件
- 在"临时记录"标题下添加列表项
- 格式：`- {输入内容}`

**文件定位策略**：
```javascript
// 基于当前文件路径提取项目名
const activeFile = app.workspace.getActiveFile();
const projectName = activeFile.path.split("/")[1];
```

### 2.2 记录知识/信息

**触发条件**：选择【2】记录知识/信息

**处理逻辑**：
- 直接打开效率工具箱文件
- 文件不存在时自动创建
- 使用默认内容结构

**目标文件路径**：
```
3-过程资产/{项目名}/@其他/效率工具箱.md
```

**默认内容**：
```
# 效率工具箱
```

### 2.3 打开改进文档

**触发条件**：选择【3】打开改进文档

**处理逻辑**：
- 直接打开改进待办事项文件
- 文件不存在时根据模板创建
- 使用 TP-Project-Improvement.md 模板

**模板路径**：
```
0-辅助/Templates/TP-Project-Improvement.md
```

## 错误处理

### 3.1 异常场景

| 错误类型 | 触发条件 | 处理策略 |
|---------|----------|----------|
| 项目路径错误 | 当前文件不在项目目录下 | 显示"请先打开一个项目文件"通知 |
| 标题定位失败 | 未找到"临时记录"标题 | 显示"未找到'临时记录'标题"通知 |
| 文件创建失败 | 模板文件不存在 | 显示"模板文件不存在"通知 |
| 用户取消操作 | 弹窗中选择取消 | 显示"已取消选择"通知 |

### 3.2 调试方法

**控制台查看**：`Ctrl + Shift + I` → Console标签页

**日志输出**：脚本会输出关键操作步骤和错误信息

```javascript
console.error("添加灵感时出错:", error);
console.error("确保文件存在时出错:", error);
```

## 技术实现

### 4.1 核心算法

**项目路径解析**：
```javascript
const projectName = activeFile.path.split("/")[1];
```

**文件存在性检查**：
```javascript
async function ensureFileExists(filePath, templateName, defaultContent) {
    let targetFile = app.vault.getAbstractFileByPath(filePath);
    if (!targetFile) {
        // 创建目录结构
        await createDirectoryStructure(dirPath);
        // 根据模板或默认内容创建文件
        // ...
    }
    return targetFile;
}
```

### 4.2 配置参数

**文件路径配置**：
```javascript
const fileConfigs = {
    效率工具箱: {
        path: `3-过程资产/${projectName}/@其他/效率工具箱.md`,
        title: "效率工具箱",
        defaultContent: "# 效率工具箱\n\n",
    },
    改进待办事项: {
        path: `2-项目/${projectName}/改进待办事项.md`,
        title: "改进待办事项",
        template: "TP-Project-Improvement.md",
    }
};
```

## 扩展指南

### 5.1 功能定制

**添加新操作类型**：
1. 修改 `tp.system.suggester` 的选项数组
2. 添加对应的处理逻辑分支
3. 配置新文件路径和模板

**修改文件格式**：
- 调整列表前缀符号
- 更改标题定位策略
- 自定义内容模板结构

### 5.2 性能优化建议

**缓存机制**：考虑添加项目路径缓存，避免重复解析
**异步处理**：文件操作保持异步，避免界面卡顿
**错误重试**：关键操作添加失败重试机制

## 使用限制

- **项目依赖**：必须在项目文件中使用（路径包含项目目录结构）
- **模板要求**：依赖标准模板文件结构
- **权限要求**：需要文件创建和编辑权限
- **Obsidian环境**：必须在Obsidian应用中运行

## 技术支持

如遇问题，请检查：
1. Templater插件是否启用
2. 模板文件是否存在
3. 文件路径权限是否足够
4. 控制台错误信息

## 更新日志

### V1.2.1（2025-9-11）：通知提醒优化
- 🔧 **移除冗余提醒**：移除不必要的成功提醒，静默执行，仅保留异常部分

### v1.2.0（2025-09-10）：错误处理优化
- 🔧 **错误处理增强**：完善了文件创建和目录结构的错误处理机制
- 🔧 **用户体验提升**：更清晰的错误提示和操作反馈
- 🔧 **代码稳定性**：增强了脚本的健壮性和容错能力
- 🔧 **名称统一**：脚本、模板名称统一

### v1.1.0（2025-08-15）：目录结构自动创建
- ✨ **自动目录创建**：新增目录结构自动创建功能，确保目标目录存在
- 🔧 **路径处理优化**：改进了文件路径解析和处理逻辑
- 🔧 **模板支持增强**：更好地支持模板文件的使用

### v1.0.0（2025-07-01）：初始版本发布
- 🎉 **初始版本发布**：支持三种操作模式
- ✅ **基本功能**：记录洞见、打开效率工具箱、访问改进文档
- ✅ **自动文件定位**：基于当前文件路径自动定位目标文件
- ✅ **模板应用**：支持使用预定义模板创建文件