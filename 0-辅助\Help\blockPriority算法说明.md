# 阻塞优先级算法技术规范

## 1. 问题定义

阻塞优先级算法旨在解决项目管理系统中阻塞型阻碍的优先级排序挑战。核心问题在于基于阻碍的本质复杂性、影响范围和时间敏感性来确定处理顺序，同时确保阻碍类型与关联对象之间的统计独立性，将趋同率控制在5%以内。

## 2. 算法设计

### 核心公式

**优先级 = 类型权重 × (1 + √(存活迭代 × 迭代频率 / 16)) × log₂(强化频率值 + 1.2) × (1 + 分级场景系数)**

**设计目标**：通过"类型权重（原因）"和"关联字段（对象）"双维度协同，实现阻碍优先级的高区分度，同时保持类型与关联的统计独立性。

## 参数详细说明

### 2.1 类型权重（Type Weight）

**定义**：阻碍的**静态优先级基础值**，**仅基于产生原因判定**（与关联对象无关），反映问题的**本质复杂性**。

|类型名称|权重值|适用场景|配置方式|
|---|---|---|---|
|硬阻塞|1.3|导致功能完全不可用的阻塞（如API接口宕机、数据库连接失败）|手动标记（如“阻塞型”标签）|
|价值威胁|1.0|常规功能缺陷或性能问题（如UI显示错位、加载速度慢）|默认值（未标记为硬阻塞/复合型）|
|**复合型**|**1.6**|**跨系统/多模块的复杂阻碍**（如跨系统API数据同步失败、多模块依赖冲突）|**基于产生原因判定**（如“跨系统”“多模块”标签或问题描述）|
### 2.2 存活迭代（Age Iterations）

**定义**：阻碍从创建到当前经历的完整迭代次数，量化问题的老化程度（存活时间越长，优先级越高）。

**计算方式**：存活迭代 = 当前迭代号 - 创建迭代号 + 1

**时间因子**（老化惩罚系数）：时间因子 = 1 + √(存活迭代 × 迭代频率 / 16)

| 存活迭代 | 时间因子 | 业务含义 |
|---|---|---|
| 1 | 1.25 | 新阻碍（1个迭代内） |
| 4 | 1.50 | 中期阻碍（2-3个迭代） |
| 9 | 1.75 | 长期阻碍（4-5个迭代） |
| 16 | 2.00 | 超期阻碍（≥8个迭代） |

**边缘情况处理**：当迭代频率变化时，系统自动重新计算时间因子以确保准确性。

### 2.3 强化频率值（Enhanced Frequency）

**定义**：**仅基于关联对象的影响力**，反映阻碍的**扩散范围**（关联对象越多、类型越重要，优先级越高）。

**计算方式**：强化频率值 = ∑(类型基数ⁿ × 数量 × 迭代频率)

其中：
- n = min(3, 关联类型数)（关联类型数越多，指数放大效果越显著，上限为3）
- **类型基数**：关联对象的影响力权重（值越大表示对项目影响越大）

| 关联类型 | 基数 | 说明 |
|---|---|---|
| 任务（Task） | 1.2 | 常规开发任务（如"用户登录功能开发"） |
| 阻碍（Blockage） | 1.5 | 被其他阻碍依赖（如"API阻塞导致支付功能阻碍"） |
| 周计划（Plan） | 2.0 | 迭代计划中标记的阻碍（如"本周计划完成的订单模块阻塞"） |
| 周评审（Review） | 2.5 | 评审会议中提出的问题（如"周评审时发现的性能瓶颈"） |
| 周回顾（Retro） | 2.2 | 回顾会议中识别的改进项（如"周回顾时发现的流程缺陷"） |

**示例**：关联2个任务（Task）+1个周评审（Review）→ 关联类型数=2 → n=2 → 强化频率值 = (1.2² × 2) + (2.5² × 1) = 2.88 + 6.25 = 9.13

**边缘情况**：当关联类型数为0时，强化频率值默认为1.0，避免除零错误。

### 2.4 分级场景系数（Tiered Scene Coefficient）

**定义**：**仅基于关联对象的紧急度**，反映阻碍的**时间敏感性**（关联会议越多、数量越多，优先级越高）。

**计算方式**：场景系数 = min(0.10(基础值) + 会议基础加成 + 多类型加成 + 关联数量加成, 0.85(上限))

| 组成部分 | 计算规则 |
|---|---|
| 基础值 | 固定0.10（无会议关联时的默认值） |
| 会议基础加成 | 按**最高优先级**会议类型取值：周评审（+0.30）> 周回顾（+0.25）> 周计划（+0.15） |
| 多类型加成 | 每增加1种会议类型（如周计划+周评审）：+0.15 |
| 关联数量加成 | 每增加1个关联（无论类型）：+0.10（总关联数-1=额外关联数） |

**示例**：关联1个周计划（Plan）+1个周评审（Review）+2个任务（Task）→ 总关联数=4 → 场景系数 = 0.10 + 0.30(周评审) + 0.15(2种会议) + 0.30(3个额外关联) = 0.85（达到上限）

**边界条件**：当无任何关联时，场景系数保持基础值0.10；上限0.85防止过度放大紧急度。

## 3. 示例验证

### 示例1：跨系统API阻塞（复合型）

**场景**：跨系统API数据同步失败（涉及用户、订单、支付模块），**类型为复合型（1.6）**，存活迭代=4，关联2个任务（Task）+1个周评审（Review）。

**计算过程**：

1. 类型权重：1.6（基于跨系统产生原因判定）；
2. 时间因子：1 + √(4 × 1 / 16) = 1 + √(0.25) = 1 + 0.5 = 1.50；
3. 强化频率值：(1.2² × 2) + (2.5² × 1) = (1.44 × 2) + (6.25 × 1) = 2.88 + 6.25 = 9.13；
4. 频率因子：log₂(9.13 + 1.2) = log₂(10.33) ≈ 3.37；
5. 场景系数：0.10 + 0.30(周评审) + 0(仅1种会议类型) + 0.20(2个额外关联) = 0.60；
6. **最终优先级**：1.6 × 1.50 × 3.37 × (1 + 0.60) = 13.01。

### 示例2：常规UI显示异常（价值威胁）

**场景**：用户头像显示错位，**类型为价值威胁（1.0）**，存活迭代=1，关联1个任务（Task）。

**计算过程**：

1. 类型权重：1.0（默认价值威胁类型）；
2. 时间因子：1 + √(1 × 1 / 16) = 1 + √(0.0625) = 1 + 0.25 = 1.25；
3. 强化频率值：1.2¹ × 1 = 1.2 × 1 = 1.2；
4. 频率因子：log₂(1.2 + 1.2) = log₂(2.4) ≈ 1.26；
5. 场景系数：0.10 + 0(无会议) + 0(0种会议类型) + 0(0个额外关联) = 0.10；
6. **最终优先级**：1.0 × 1.25 × 1.26 × (1 + 0.10) = 1.73。

**结论**： 复合型阻碍（示例1，优先级13.01）**远高于**价值威胁阻碍（示例2，优先级1.73），即使关联数量少，仍能通过**类型权重（1.6）**准确反映其**本质复杂性**；关联字段（任务、周评审）仅影响**强化频率值**和**场景系数**，不干扰类型权重的判定。

## 4. 实现细节与边缘情况处理

### 4.1 类型与关联独立原则

- **类型权重判定**：仅基于阻碍的产生原因（如跨系统/多模块特性），完全独立于关联对象
- **关联字段计算**：仅基于阻碍的关联对象（任务、会议等），完全独立于产生原因
- **协同作用**：类型权重反映本质复杂性，关联字段反映扩散范围与紧急度，两者正交协同

### 4.2 动态调整机制

- **趋同率监控**：当阻塞型阻碍趋同率超过5%阈值时，系统自动触发基数调整
- **基数调整**：按0.1步长调整类型基数（如任务基数1.2→1.3），仅影响强化频率值
- **权重保护**：类型权重保持不变，确保本质复杂性评估的稳定性

### 4.3 数据质量保障

- **自动同步**：迭代号、关联类型、存活迭代等数据从项目管理工具（Jira等）自动采集
- **人工校验**：类型权重（特别是复合型）需要人工确认问题描述或标签，避免算法误判
- **异常处理**：数据缺失时采用默认值，确保计算连续性

### 4.4 边界条件处理

- **零关联处理**：当无任何关联对象时，强化频率值=1.0，场景系数=0.10
- **除零防护**：所有数学运算包含边界检查，避免除零错误和数值溢出
- **上限控制**：关键参数设置合理上限，防止过度放大单一因素影响

## 五、版本历史

| 版本 | 修订内容 | 日期 |
|---|---|---|
| 4.0 | 1. 移除复合型阻碍"需关联≥3种类型"的强制要求；2. 明确"类型权重与关联字段独立"原则；3. 优化边缘情况处理 | 2025-08-15 |
| 3.0 | 修正场景系数计算逻辑，明确多类型加成规则 | 2025-08-06 |
| 2.0 | 新增强化频率值的指数计算规则 | 2025-07-28 |
| 1.0 | 初始版本发布 | 2025-07-15 |

**算法稳定性**：经过多次修订，当前版本在保持≤5%趋同率的同时，解决了类型与关联绑定的逻辑漏洞，具备良好的可扩展性和维护性。