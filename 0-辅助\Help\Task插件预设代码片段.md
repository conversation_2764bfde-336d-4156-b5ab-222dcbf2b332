name：project_improvements「每周计划」
preset：
```
limit 5
  heading includes 行动
  description regex matches /\S/
  hide backlink
  is not blocked
  sort by priority
  filter by function \
  const projectName = query.file.path.split("/")[1]; \
  const targetPath = `2-项目/${projectName}/改进待办事项`; \
  const pathMatches = task.file.path.includes(targetPath);\
  return pathMatches;
```

name：project_tasks_todo「每日执行」
preset：
```
hide backlink
hide created date
hide scheduled date
filter by function \
const fileName = query.file.filenameWithoutExtension;\
const dateMatch = fileName.match(/\d{4}-\d{2}-\d{2}/);\
if (!dateMatch) return false;\
const targetDate = moment(dateMatch[0]);\
const year = targetDate.isoWeekYear();\
const weekNumber = targetDate.isoWeek().toString().padStart(2, '0');\
const targetPath = `Plan-${year}-WK${weekNumber}`;\
const pathMatches = task.file.path.includes(targetPath);\
const titleKeywords = "任务";\
const headingMatches = task.heading && task.heading.includes(titleKeywords);\
const dateMatches = task.scheduled?.moment?.isSame(targetDate, 'day');\
const isWeekPlanTask = pathMatches && headingMatches && dateMatches;\
const currentPath = query.file.path;\
const projectName = currentPath ? currentPath.split("/")[1] : "";\
const techDebtPath = `3-过程资产/${projectName}/技术债`;\
const isTechDebtTask = projectName && \
task.file && task.file.path && \
task.file.path.includes(techDebtPath) && dateMatches && \
task.description?.trim().length > 0;\
return Boolean(isWeekPlanTask) || Boolean(isTechDebtTask);
```

name：project_tasks_unplanned「每日执行」
preset：
```
not done
no scheduled date
hide backlink
filter by function \
const fileName = query.file.filenameWithoutExtension;\
const dateMatch = fileName.match(/\d{4}-\d{2}-\d{2}/);\
if (!dateMatch) return false;\
const targetDate = moment(dateMatch[0]);\
const year = targetDate.isoWeekYear();\
const weekNumber = targetDate.isoWeek().toString().padStart(2, '0');\
const targetPath = `Plan-${year}-WK${weekNumber}`;\
const pathMatches = task.file.path.includes(targetPath);\
const titleKeywords = "任务";\
const headingMatches = task.heading && task.heading.includes(titleKeywords);\
const hasDescription = task.description?.trim().length > 0;\
const isWeekPlanTask = pathMatches && headingMatches && hasDescription;\
const currentPath = query.file.path;\
const projectName = currentPath ? currentPath.split("/")[1] : "";\
const techDebtPath = `3-过程资产/${projectName}/技术债`;\
const isTechDebtTask = projectName && task.file && task.file.path && \
task.file.path.includes(techDebtPath) && \
task.created && \
task.created.moment && \
task.created.moment.isoWeek().toString().padStart(2, '0') === weekNumber && \
task.description?.trim().length > 0;\
return Boolean(isWeekPlanTask) || Boolean(isTechDebtTask);
```