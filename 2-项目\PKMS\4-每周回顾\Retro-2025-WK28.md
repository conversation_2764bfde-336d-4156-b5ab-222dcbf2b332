---
homePageLink: "[[PKMS-首页]]"
---
# 1. 类型分析
> [!dashboard]
> 
> > [!tip] 阻碍
> >```dataviewjs
> >// 定义目标文件夹和文件名格式
> >const projectName = dv.current().file.path.split("/")[1];
> >const folder = `3-过程资产/${projectName}/阻碍`;
> >const pattern = /^blocker-\d{8}-\d{2}$/;
> >const allFiles = dv.pages(`"${folder}"`);
> >const combinedType = "";
> >// 筛选符合文件名格式且状态非"已关闭"的文件
> >const filteredFiles = allFiles.filter(p => {
> >    const name = p.file.name.replace(/\.md$/, ""); 
> >    return pattern.test(name) && p.status !== "已关闭";
> >});
> >if (filteredFiles.length === 0) {
> >    dv.paragraph("没有找到符合条件的阻碍文件");
> >} else {
> >    const specialFiles = filteredFiles.filter(p => !p.type || p.type === combinedType);
> >	// 输出特殊文件（无type或type为组合值）
> >    if (specialFiles.length > 0) {
> >        dv.el("p",`当前共计 **${specialFiles.length}** 个文件的类型未被定义或无效`)
> >        dv.paragraph(specialFiles.map(p => `[[${p.file.path}|${p.aliases}]]`));
> >    }else{
> >	    const groups = {};
> >	    filteredFiles.forEach(p => {
> >	        const type = p.type || "未分类";
> >	        if (!groups[type]) groups[type] = [];
> >	        groups[type].push(p);
> >	    });
> >	    // 按数量排序
> >	    const sortedGroups = Object.entries(groups)
> >	        .map(([type, files]) => ({
> >	            type,
> >	            count: files.length,
> >	            files
> >	        }))
> >	        .sort((a, b) => b.count - a.count);
> >	    // 取前三组
> >	    const topGroups = sortedGroups.slice(0, 3);
> >	    // 显示分组结果（列表形式）
> >	    topGroups.forEach(group => {
> >	        dv.el("p", `${group.type} (${group.count})`);
> >	        dv.paragraph(group.files.map(p => { return `[[${p.file.path}|${p.aliases}]]`}));
> >	    });
> >    }
> >}
> >```
> 
> > [!warning] 技术债
> > ```dataviewjs
> >// 定义目标文件夹和文件名格式
> >const projectName = dv.current().file.path.split("/")[1];
> >const folder = `3-过程资产/${projectName}/技术债`;
> >const pattern = /^td-\d{8}-\d{2}$/;
> >const allFiles = dv.pages(`"${folder}"`);
> >const combinedType = "阻塞型/成本型/战略型/无害型";
> >// 筛选符合文件名格式且状态非"已关闭"的文件
> >const filteredFiles = allFiles.filter(p => {
> >    const name = p.file.name.replace(/\.md$/, ""); 
> >    return pattern.test(name) && p.status !== "已关闭";
> >});
> >if (filteredFiles.length === 0) {
> >    dv.paragraph("没有找到符合条件的技术债文件");
> >} else {
> >    const specialFiles = filteredFiles.filter(p => !p.type || p.type === combinedType);
> >	// 输出特殊文件（无type或type为组合值）
> >    if (specialFiles.length > 0) {
> >        dv.el("p",`当前共计 **${specialFiles.length}** 个文件的类型未被定义或无效`)
> >        dv.paragraph(specialFiles.map(p => `[[${p.file.path}|${p.aliases}]]`));
> >    }else{
> >	    const groups = {};
> >	    filteredFiles.forEach(p => {
> >	        const type = p.type || "未分类";
> >	        if (!groups[type]) groups[type] = [];
> >	        groups[type].push(p);
> >	    });
> >	    // 按数量排序
> >	    const sortedGroups = Object.entries(groups)
> >	        .map(([type, files]) => ({
> >	            type,
> >	            count: files.length,
> >	            files
> >	        }))
> >	        .sort((a, b) => b.count - a.count);
> >	    // 取前三组
> >	    const topGroups = sortedGroups.slice(0, 3);
> >	    // 显示分组结果（列表形式）
> >	    topGroups.forEach(group => {
> >	        dv.el("p", `${group.type} (${group.count})`);
> >	        dv.paragraph(group.files.map(p => {return `[[${p.file.path}|${p.aliases}]]`}));
> >	    });
> >    }
> >}
> > ```

# 2. 流程改善

| 来源                      | 根因分析           | 改善行动                                                                                                                               | 验收标准                                                                                                                        | 验收结果 |
| ----------------------- | -------------- | ---------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------- | ---- |
| [[blocker-20250707-02]] | 缺乏对流程框架类型问题的判断 | 1、遇到阻碍时自问<br>（1）是否步骤模糊，无法描述下一步具体动作？<br>（2）是否因流程缺失/冲突导致阻塞？<br>（3）本周是否已遇到≥1次同类卡点？<br>2、 IF达2个"是" ，周回顾处理；否则，“制定临时方案并执行（需确保任务24h内不阻塞）” | 在周回顾会议上，审查本周所有阻碍记录，评估决策框架的执行效果：<br>（1）框架应用率（是否对每个阻碍进行了自问）是否 ≥ 85%？<br>（2）决策准确性（是否符合IF条件）是否 ≥ 85%？<br>（3）流程完整执行率是否提升至 ≥ 70%？ |      |
| [[blocker-20250630-01]] | 缺乏对知识类问题的判断    | 1、遇到阻碍时自问<br>（1）是否因“不知道怎么做”引发？ <br>（2）解决方案是否可复用于≥3个类似场景？ <br>（3）短时间学习能否见效？  <br>2、IF达到3个“是”，立即行动；IF“若立即行动后1天内未解决”，自动升级至周回顾         | 在下个工作周，记录所有遇到的阻碍事件，监控自问框架的应用情况（包括应用次数和决策结果），并在周回顾会议上审查：应用率是否 ≥ 80%？决策正确性（符合IF条件）是否 ≥ 90%？                                   |      |
# 3. 改善回顾

```dataviewjs
await dv.view("0-辅助/Views/improveSummary", { dv });
```
# 4. 改进项梳理
```dataviewjs
	const projectName = dv.current().file.path.split("/")[1];
	const path = `2-项目/${projectName}/改进待办事项.md`;
	const targetPage = dv.page(path);
	if(!targetPage){
		dv.el("p",`❗️ 路径错误：${path}`)
	}else{
		const lists = targetPage.file.lists.filter(l => l.header.subpath.includes("缓存区") && l.text)
		if(lists.length > 0){
			dv.el("p",`当前共计 ${lists.length} 条灵感未梳理 [[${path}|🔗]]`)
			dv.paragraph(lists.text)
		}else{
			dv.el("p",`🎉 所有灵感已梳理完成`)
		}
	}
```
# 5. 成功经验

| 超预期成果 | 根因分析 | 关键行为 | 证据链 | 经验封装 | 复用场景 |
| ----- | ---- | ---- | --- | ---- | ---- |
|       |      |      |     |      |      |