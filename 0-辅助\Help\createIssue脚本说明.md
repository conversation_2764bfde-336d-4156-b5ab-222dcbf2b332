# createIssue 脚本使用说明

## 功能概述

createIssue 脚本提供统一的问题创建和管理能力，支持快速创建技术债和阻碍日志：

| 功能模块   | 功能描述                  | 技术实现            | 适用场景            |
| ------ | --------------------- | --------------- | --------------- |
| 问题类型选择 | 启动时弹出选择框让用户选择创建类型     | 原生单选框界面         | 新建技术债或阻碍前的类型确认  |
| 项目识别   | 从当前活动文件路径自动提取项目名称     | 路径解析算法          | 自动定位目标项目目录      |
| 文件创建   | 在对应目录中按规范格式创建文件       | 模板应用 + 序号管理     | 技术债/阻碍文件的标准化创建  |
| 智能文本处理 | 自动检测选中文本并转换为内部链接（仅阻碍） | 文本选择检测 + 链接替换   | 快速将问题描述转换为可追踪链接 |
| 属性设置   | 自动设置创建日期、状态和用户别名      | YAML 前置元数据处理    | 文件元数据的自动化配置     |
| 文件打开   | 在新标签页中自动打开创建的文件       | Obsidian API 调用 | 创建后立即编辑和查看      |

## 文件结构

```
0-辅助/
├── Scripts/
│   ├── createIssue.js               # 核心脚本实现（合并版）
│   ├── createTechDebt.js            # 原技术债脚本（已合并）
│   ├── createdBlocker.js            # 原阻碍脚本（已合并）
│   ├── createIssue脚本说明.md       # 本文档
│   ├── createTechDebt脚本说明.md    # 原技术债文档
│   └── createdBlocker脚本说明.md    # 原阻碍文档
└── Templater/
    ├── Function/
    │   ├── createIssue.md           # Templater调用接口（合并版）
    │   ├── createTechDebt.md        # 原技术债接口
    │   └── createdBlocker.md        # 原阻碍接口
    └── Notes/
        ├── TP-Project-TechDebt.md   # 技术债模板文件
        └── TP-Project-Blocker.md    # 阻碍日志模板文件
```

## 使用流程

### 1. 快捷键配置

**操作路径**：Obsidian设置 → 快捷键 → 搜索"createIssue"

**推荐快捷键**：`Ctrl + Shift + I`（Issue首字母）

### 2. 操作流程

```
快捷键触发 → 选择问题类型 → 自动提取项目信息 → 输入别名 → 创建文件 → 自动打开文件
```

**步骤说明**：
1. 在项目文件中按下快捷键
2. 在弹出的选择框中选择【1】新建阻碍 或 【2】新建技术债
3. 脚本自动提取项目名称和路径信息
4. 弹出对话框输入问题别名（可选）
5. 自动创建对应类型的文件并应用模板
6. 在新标签页中打开创建的文件

## 功能详解

### 3.1 问题类型选择

**触发条件**：脚本执行时首先显示选择界面

**选择界面**：
- 【1】新建阻碍：创建阻碍日志文件
- 【2】新建技术债：创建技术债文件

**处理逻辑**：
```javascript
const choice = await tp.system.suggester(
    ["【1】新建阻碍", "【2】新建技术债"],
    ["blocker", "techDebt"],
    false,
    "请选择要创建的类型"
);
```

### 3.2 项目识别与目录定位

**触发条件**：用户确认问题类型后自动执行

**处理逻辑**：
- 获取当前活动文件路径
- 从路径中提取第二级目录作为项目名称
- 验证路径有效性
- 构建目标目录路径

**路径解析算法**：
```javascript
const activeFile = tp.file.find_tfile(tp.file.title);
const currentPath = activeFile.path;
const projectName = currentPath.split("/")[1];
```

**目标目录结构**：
- 阻碍日志：`3-过程资产/{项目名称}/阻碍`
- 技术债：`3-过程资产/{项目名称}/技术债`

### 3.3 文件创建与管理

**文件命名规则**：

| 问题类型 | 命名格式 | 示例 |
|---------|----------|------|
| 阻碍日志 | `blocker-YYYYMMDD-序号.md` | `blocker-20250911-01.md` |
| 技术债 | `td-YYYYMMDD-序号.md` | `td-20250911-01.md` |

**序号管理逻辑**：
```javascript
const existingFiles = app.vault.getFiles().filter(
    file => file.path.startsWith(targetDir) &&
            file.name.startsWith(`${prefix}-${dateStr}-`) &&
            file.name.endsWith(".md")
);
const nextNumber = existingFiles.length + 1;
```

### 3.4 智能文本处理（阻碍专属）

**功能特性**：
- **自动检测选中文本**：当在文件中选中文本后运行脚本，系统自动识别选中内容
- **智能别名填充**：选中的文本自动填充到别名输入框作为默认值
- **文本链接化**：创建文件后，选中文本被替换为 `[[文件名|别名]]` 格式的内部链接
- **取消保护**：用户取消操作时，选中文本会被恢复

**使用场景**：
- 在记录日常工作时发现需要创建阻碍的问题
- 选中问题描述文本，运行脚本
- 系统自动创建阻碍文件并将选中文本转换为链接

### 3.5 模板应用与属性设置

**模板路径**：
- 阻碍日志：`0-辅助/Templater/Notes/TP-Project-Blocker.md`
- 技术债：`0-辅助/Templater/Notes/TP-Project-TechDebt.md`

**自动填充内容**：

| 属性 | 阻碍日志 | 技术债 | 设置方式 |
|------|---------|--------|----------|
| `createdDate` | 当前日期 | 当前日期 | 自动设置 |
| `aliases` | 用户输入/选中文本 | 用户输入 | 用户输入或选中文本 |
| `status` | "进行中" | "待处理" | 根据类型自动设置 |

**模板处理逻辑**：
```javascript
// 替换创建日期
content = content.replace('createdDate: ""', `createdDate: "${createdDate}"`);

// 替换别名
if (alias) {
    content = content.replace("aliases:", `aliases: "${alias}"`);
}

// 设置状态
content = content.replace("status: 进行中/待验证/已关闭", "status: 进行中");
```

## 参数配置

### 4.1 模板配置

```javascript
const TEMPLATES = {
    BLOCKER: "0-辅助/Templater/Notes/TP-Project-Blocker.md",
    TECH_DEBT: "0-辅助/Templater/Notes/TP-Project-TechDebt.md",
};
```

### 4.2 返回信息配置

```javascript
const COMMENTS = {
    CANCELLED: "<!-- 操作已取消 -->",
    PROJECT_INFO_FAILED: "<!-- 获取项目信息失败 -->",
    TEMPLATE_NOT_FOUND: "<!-- 模板文件不存在 -->",
    ALIAS_CANCELLED: "<!-- 用户取消别名输入 -->",
    TYPE_SELECTION_CANCELLED: "<!-- 用户取消类型选择 -->",
};
```

### 4.3 路径配置

```javascript
// 基础目录结构
const targetDir = `3-过程资产/${projectName}`;
// 子目录
const blockerDir = `${targetDir}/阻碍`;
const techDebtDir = `${targetDir}/技术债`;
```

## 返回值

### 5.1 成功返回

| 问题类型 | 返回值 | 说明 |
|---------|--------|------|
| 阻碍日志 | TFile 对象 | 新创建的阻碍文件对象 |
| 技术债 | TFile 对象 | 新创建的技术债文件对象 |

### 5.2 失败返回

| 场景 | 返回值 | 说明 |
|------|--------|------|
| 用户取消类型选择 | `"<!-- 用户取消类型选择 -->"` | 用户在选择界面取消 |
| 用户取消别名输入 | `"<!-- 用户取消别名输入 -->"` | 用户在别名对话框取消 |
| 项目信息获取失败 | `"<!-- 获取项目信息失败 -->"` | 无法获取当前项目信息 |
| 模板文件不存在 | `"<!-- 模板文件不存在 -->"` | 模板文件缺失 |
| 操作已取消 | `"<!-- 操作已取消 -->"` | 一般性取消操作 |

## 异常处理

### 6.1 异常场景

| 错误类型 | 触发条件 | 处理策略 | 用户提示 |
|---------|----------|----------|----------|
| 活动文件缺失 | 无当前活动文件 | 终止执行 | "未找到当前活动文件" |
| 项目路径无效 | 文件不在项目目录下 | 终止执行 | "无法提取项目名称，请确保文件在'2-项目/项目名称'目录下" |
| 模板文件缺失 | 模板文件不存在 | 终止执行 | "模板文件不存在: {模板路径}" |
| 用户取消选择 | 类型选择对话框取消 | 终止执行 | 无提示（静默取消） |
| 用户取消输入 | 别名对话框取消 | 终止执行 | 无提示（静默取消） |
| 目录创建失败 | 权限不足或路径无效 | 抛出错误 | "创建目录时出错: {错误信息}" |
| 文件创建失败 | 权限不足或磁盘空间不足 | 抛出错误 | "创建文件时出错: {错误信息}" |

### 6.2 调试方法

**控制台查看**：`Ctrl + Shift + I` → Console标签页

**关键调试信息输出**：
```javascript
console.log("当前文件路径:", currentPath);
console.log("提取的项目名称:", projectName);
console.log("将创建文件:", filePath);
console.log("用户输入的别名:", alias);
console.log("选择的类型:", choice);
console.error("创建文件时出错:", error);
```

## 使用示例

### 7.1 基本使用

**场景**：在项目文件中快速创建技术债
1. 打开项目文件
2. 按下快捷键 `Ctrl + Shift + I`
3. 选择【2】新建技术债
4. 输入别名："数据库性能优化"
5. 系统自动创建文件并在新标签页打开

### 7.2 智能文本处理

**场景**：选中问题描述创建阻碍日志
1. 在项目文件中选中问题描述文本
2. 按下快捷键 `Ctrl + Shift + I`
3. 选择【1】新建阻碍
4. 别名输入框自动填充选中文本
5. 确认或修改别名后创建文件
6. 选中文本自动转换为 `[[blocker-20250911-01|问题描述]]` 链接

### 7.3 取消操作

**场景**：用户中途取消创建
1. 按下快捷键 `Ctrl + Shift + I`
2. 在选择界面按 ESC 键取消
3. 或在别名输入界面点击取消
4. 活动文件保持原样，无任何修改

## 扩展指南

### 8.1 功能定制

**添加新属性**：
1. 修改对应的模板文件（`TP-Project-Blocker.md` 或 `TP-Project-TechDebt.md`）
2. 在脚本中添加对应的处理逻辑
3. 考虑是否需要用户输入或自动设置

**修改文件命名规则**：
- 调整前缀从"blocker-"或"td-"改为其他标识符
- 更改日期格式或序号规则
- 添加项目名称前缀

**自定义模板路径**：
```javascript
// 可配置化模板路径
const TEMPLATES = {
    BLOCKER: config.blockerTemplate || "0-辅助/Templater/Notes/TP-Project-Blocker.md",
    TECH_DEBT: config.techDebtTemplate || "0-辅助/Templater/Notes/TP-Project-TechDebt.md",
};
```

### 8.2 性能优化建议

**缓存机制**：对项目路径解析结果进行缓存，避免重复计算
**批量处理**：支持批量创建多个问题文件
**模板预加载**：在脚本初始化时预加载模板内容
**错误重试**：对文件操作添加重试机制，提高稳定性

## 使用限制

### 9.1 环境依赖

- **Obsidian要求**：必须在Obsidian应用中运行
- **插件依赖**：需要启用Templater插件
- **权限要求**：需要文件创建、读取、写入权限

### 9.2 路径约束

- **项目结构**：必须使用标准的"2-项目/项目名称/..."目录结构
- **模板位置**：模板文件必须位于指定路径
- **目标目录**：问题文件创建在固定的"3-过程资产/项目名称/{阻碍|技术债}"目录

### 9.3 性能考虑

- **文件操作**：涉及多个异步文件操作，可能有一定延迟
- **模板大小**：模板文件不宜过大，建议保持在10KB以内
- **并发限制**：不建议同时运行多个实例，避免文件冲突

## 技术支持

### 10.1 常见问题排查

**问题**：脚本无法找到当前活动文件
- 检查是否有文件处于活动状态
- 重新打开文件后再尝试运行脚本

**问题**：无法提取项目名称
- 确认文件路径包含"2-项目/项目名称"结构
- 检查文件是否在正确的项目目录下

**问题**：模板文件不存在
- 检查模板文件是否存在于指定路径
- 确认文件路径和名称正确

**问题**：目录创建失败
- 检查是否有足够的文件系统权限
- 确认目标路径不存在非法字符

### 10.2 日志分析

**控制台日志级别**：
- `console.log()`: 正常操作信息
- `console.error()`: 错误和异常信息

**关键日志信息**：
- 当前文件路径和提取的项目名称
- 选择的类型和目标文件路径
- 用户输入的别名内容
- 错误堆栈信息

## 更新日志

### V3.0.0（2025-9-11）：合并版本发布
- 🔄 **脚本合并**：将 createTechDebt 和 createdBlocker 合并为 createIssue
- ✨ **类型选择**：新增启动时的原生单选框选择界面
- 🔧 **零写入保证**：用户取消操作时绝对不在活动文件写入任何内容
- 📖 **文档合并**：合并两份说明文档，统一结构和术语
- 🎯 **功能保持**：完全保留原有功能，零改动参数和输出

### V2.1.2（2025-9-11）：通知提醒优化
- 🔧 **移除冗余提醒**：移除不必要的成功提醒，静默执行，仅保留异常部分

### v2.1（2025-09-10）：智能文本处理功能
- ✨ **新增文本选择检测**：自动识别鼠标选中的文本
- ✨ **智能别名填充**：选中文本自动作为别名默认值
- ✨ **自动文本链接化**：选中文本替换为内部链接格式

### v2.0（2025-07-18）：模块化重构
- 🏗️ **代码模块化**：将功能拆分为独立函数，提高可维护性
- 🔧 **错误处理优化**：完善的异常处理和用户反馈机制

### v1.0（2025-07-01）：初始版本发布
- 🎉 **基本功能**：支持问题文件的创建和管理
- ✅ **自动属性设置**：创建日期、别名、状态自动设置
- ✅ **模板应用**：使用预定义模板
- ✅ **文件自动打开**：创建后自动在新标签页打开文件