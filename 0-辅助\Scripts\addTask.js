/**
 * 快速添加任务到周计划
 * 功能：
 * 1. 检测当前鼠标是否选中内容
 * 2. 若没有内容则弹出输入框，要求用户输入内容；若选中了内容则将其作为用户输入内容
 * 3. 检查当前已经打开的所有文件
 * 4. 找到满足格式：Plan-\d{4}-WK\d{2}的文件
 * 5. 将用户输入以格式：-[] 用户输入，添加到目标文件的"任务拆解"标题的最下方
 */

module.exports = async (tp) => {
  try {
    // 步骤1：获取用户输入内容
    let taskContent = "";
    
    // 检查是否有选中的文本
    const activeEditor = app.workspace.activeEditor;
    let originalSelection = "";
    if (activeEditor && activeEditor.editor) {
      const selection = activeEditor.editor.getSelection();
      if (selection && selection.trim() !== "") {
        taskContent = selection.trim();
        originalSelection = selection; // 保存原始选中内容用于恢复
      }
    }
    
    // 如果没有选中内容，弹出输入框
    if (!taskContent) {
      taskContent = await tp.system.prompt("请输入任务内容:");
      if (!taskContent || taskContent.trim() === "") {
        return null;
      }
      taskContent = taskContent.trim();
    }
    
    // 步骤2：查找匹配的周计划文件
    const planFiles = await findPlanFiles();
    if (planFiles.length === 0) {
      new Notice("未找到周计划文件，请先打开一个Plan-YYYY-WKXX格式的文件");
      return null;
    }
    
    // 如果有多个匹配文件，让用户选择
    let targetFile = planFiles[0];
    if (planFiles.length > 1) {
      const fileNames = planFiles.map(file => file.name);
      const selectedName = await tp.system.suggester(fileNames, fileNames);
      if (!selectedName) {
        return null;
      }
      targetFile = planFiles.find(file => file.name === selectedName);
    }
    
    // 步骤3：添加任务到"任务拆解"部分
    const success = await addTaskToPlan(targetFile, taskContent, originalSelection, activeEditor);
    if (success) {
      new Notice(`✅ 已添加任务到 ${targetFile.name}`);
      return true;
    } else {
      new Notice(`❌ 添加任务失败`);
      return false;
    }
    
  } catch (error) {
    console.error("添加任务时出错:", error);
    new Notice(`添加失败: ${error.message}`);
    return null;
  }
  
  // 查找Plan文件的辅助函数
  async function findPlanFiles() {
    const planFiles = [];
    const planRegex = /^Plan-\d{4}-WK\d{2}$/;
    
    // 获取所有打开的标签页
    const leaves = app.workspace.getLeavesOfType("markdown");
    
    for (const leaf of leaves) {
      const file = leaf.view?.file;
      if (file && planRegex.test(file.basename)) {
        planFiles.push(file);
      }
    }
    
    return planFiles;
  }
  
  // 添加任务到计划的辅助函数
  async function addTaskToPlan(file, taskContent, originalSelection, activeEditor) {
    try {
      // 读取文件内容
      const content = await app.vault.read(file);
      
      // 查找"任务拆解"标题
      const taskHeadingIndex = content.indexOf("任务拆解");
      if (taskHeadingIndex === -1) {
        new Notice(`在 ${file.name} 中未找到"任务拆解"标题`);
        return false;
      }
      
      // 找到标题行的开始位置
      let headingStart = taskHeadingIndex;
      while (headingStart > 0 && content[headingStart] !== '\n') {
        headingStart--;
      }
      if (content[headingStart] === '\n') headingStart++;
      
      // 找到标题行的结束位置
      let headingEnd = taskHeadingIndex + "任务拆解".length;
      while (headingEnd < content.length && content[headingEnd] !== '\n') {
        headingEnd++;
      }
      
      const headingContent = content.substring(headingStart, headingEnd);
      
      // 找到任务列表的末尾（在标题之后的内容中查找最后一个任务项）
      const nextHeadingPos = content.indexOf("#", headingEnd);
      const tasksSectionEnd = nextHeadingPos !== -1 ? nextHeadingPos : content.length;
      
      // 找到任务区域的末尾（最后一个字符位置）
      const insertPos = tasksSectionEnd;
      
      // 创建新任务条目
      let newTask = `- [ ] ${taskContent}`;
      
      // 确保在适当的位置添加换行符
      if (insertPos > headingEnd) {
        // 在现有内容后添加
        newTask = "\n" + newTask;
      } else {
        // 在标题后首次添加任务
        newTask = "\n\n" + newTask;
      }
      
      // 更新文件内容
      const newContent = 
        content.substring(0, insertPos) + 
        newTask + 
        content.substring(insertPos);
      
      // 写入文件
      await app.vault.modify(file, newContent);
      
      // 恢复选中的内容（如果有）
      if (originalSelection && activeEditor && activeEditor.editor) {
        activeEditor.editor.setSelection(activeEditor.editor.getCursor());
      }
      
      return true;
      
    } catch (error) {
      console.error("添加任务到文件时出错:", error);
      throw error;
    }
  }
};