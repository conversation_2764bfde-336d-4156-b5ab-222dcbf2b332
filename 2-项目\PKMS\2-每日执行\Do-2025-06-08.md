---
homePageLink: "[[PKMS-首页]]"
---
# 1. 任务安排
> [!dashboard]
> 
> > [!todo] 今日代办
> > ```tasks
> > hide backlink
> > filter by function \
> > const fileName = query.file.filenameWithoutExtension;\
> > const dateMatch = fileName.match(/\d{4}-\d{2}-\d{2}/);\
> > if (!dateMatch) return false;\
> > const targetDate = moment(dateMatch[0]);\
> > const year = targetDate.isoWeekYear();\
> > const weekNumber = targetDate.isoWeek().toString().padStart(2, '0');\
> > const targetPath = `Plan-${year}-WK${weekNumber}`;\
> > const pathMatches = task.file.path.includes(targetPath);\
> > const headingMatches = task.heading === "4. 关联任务";\
> > let dateMatches = false;\
> > if (task.scheduled && task.scheduled.moment) {\
> > dateMatches = task.scheduled.moment.isSame(targetDate, 'day')}\
> > return pathMatches && headingMatches && dateMatches;
> > ```
> 
> > [!tip] 未规划
> > ```tasks
> > not done
> > no scheduled date
> > hide backlink
> > filter by function \
> > const fileName = query.file.filenameWithoutExtension;\
> > const dateMatch = fileName.match(/\d{4}-\d{2}-\d{2}/);\
> > if (!dateMatch) return false;\
> > const targetDate = moment(dateMatch[0]);\
> > const year = targetDate.isoWeekYear();\
> > const weekNumber = targetDate.isoWeek().toString().padStart(2, '0');\
> > const targetPath = `Plan-${year}-WK${weekNumber}`;\
> > const pathMatches = task.file.path.includes(targetPath);\
> > const headingMatches = task.heading === "4. 关联任务";\
> > const hasContent = task.description?.trim().length > 0;\
> > return pathMatches && headingMatches && hasContent;
> > ```


# 2. 阻碍/[[字段提示信息表#技术债 7ffbc6|技术债]] 

- 

# 3. [[字段提示信息表#闪念描述：[触发场景] + [核心方案] + [价值]​​ ec14e2|闪念]]/输出

