// 测试表格修复逻辑
const testContent = `# 2. KR进度

| 序号  | 成果（✅） | 关联KR | 价值描述 | 风险预警 | 调整建议 |
| --- | :---- | ---- | ---- | ---- | ---- |
| 1   |       |      |      |      |      |`;

const projectName = "测试项目";

// 模拟修复逻辑
const lines = testContent.split("\n");
const modifiedLines = lines.map((line) => {
  console.log("处理行:", line);
  // 匹配数据行：以 | 数字 | 开头的行
  if (/^\|\s*\d+\s*\|/.test(line)) {
    console.log("匹配到数据行");
    // 分割表格列
    const columns = line.split("|").map((col) => col.trim());
    console.log("列数据:", columns);
    // 如果第3列（索引为3，因为第0列是空的）是空的，则替换
    if (columns.length >= 4 && columns[3] === "") {
      console.log("替换第3列");
      columns[3] = `[[${projectName}-首页#1. OKR设定|关联KR]]`;
      // 重建表格行，去掉第一个空元素
      return "| " + columns.slice(1).join(" | ") + " |";
    }
  }
  return line;
});

const result = modifiedLines.join("\n");
console.log("\n原始内容:");
console.log(testContent);
console.log("\n修复后内容:");
console.log(result);
