---
homePageLink: "[[PKMS-首页]]"
---
# 1. 周目标

- 探索改善行动执行的其他可行性路径
- 探索阻碍优先级排序的可行性方案

# 2. 验收标准

| 序号  | 交付物                          | 标准/通过条件                                                                                                                      | 备注  |
| --- | ---------------------------- | ---------------------------------------------------------------------------------------------------------------------------- | --- |
| 1   | 「[[改善工作流程图.svg]]」V2.1        | 1、流程图增加“改善行动”处理路径2（临时处理）<br>2、新增路径节点间逻辑通畅（无逻辑矛盾或断点质疑）                                                                        |     |
| 2   | 「[[TP-Project-Blocker]]」V1.3 | 与优先级相关的元数据字段需满足以下要求：<br>（1）设计合理、无认知负担<br>（2）填写单个字段值的时间花费＜3s                                                                  |     |
| 3   | 「[[blockPriority算法说明]]」              | 1、包含明确的阻碍优先级定逻辑/规则/框架<br>2、优先级判定逻辑覆盖80%常见场景（阻碍覆盖多个迭代、阻塞型阻碍优先级趋同）                                                             |     |
| 4   | 「[[TP-Project-Retro]]」V1.3   | “阻碍”统计看板需满足以下要求：<br>（1）完全符合「[[TP-Project-Blocker]]」V1.3中的优先级判定逻辑/规则/框架<br>（2）仅展示高优先级TOP3信息<br>（3）代码统计结果与人工评估结果相同（或匹配准确率＞98%） |     |
# 3. 改进事项
```tasks
limit 5
heading includes 行动
description regex matches /\S/
hide backlink
is not blocked
priority is not none
sort by priority
filter by function \
const projectName = query.file.path.split("/")[1]; \
const targetPath = `2-项目/${projectName}/改进待办事项`; \
const pathMatches = task.file.path.includes(targetPath);\
return pathMatches;
```

# 4. 技术债
```dataviewjs
	// 统一配置：类型和优先级设置
	const TECH_DEBT_CONFIG = {
	    types: {
	        presets: ["阻塞型", "成本型", "战略型", "无害型"],
	        get set() { return new Set(this.presets); }
	    },
	    priorities: {
	        presets: ["立即", "高", "中", "低"],
	        get set() { return new Set(this.presets); },
	        get order() { 
	            const order = {};
	            this.presets.forEach((p, i) => order[p] = i + 1);
	            return order;
	        }
	    }
	};
	
	// 获取技术债文件
	const projectName = dv.current().file.path.split("/")[1];
	const folderPath = `3-过程资产/${projectName}/技术债`;
	const allTechDebtFiles = dv.pages(`"${folderPath}"`)
	    .filter(p => 
	        p.file.name.match(/^td-\d{8}-\d{2}$/) && 
	        p.status !== "已解决" &&
	        p.file.name !== "dashboard"
	    );
	
	// 检测非预设值文件
	const hasInvalidValue = (file) => {
	    const type = file.type?.toString();
	    const priority = file.priority?.toString();
	    
	    return !TECH_DEBT_CONFIG.types.set.has(type) || 
	           !TECH_DEBT_CONFIG.priorities.set.has(priority);
	};
	
	const invalidFiles = allTechDebtFiles.filter(hasInvalidValue);
	
	// 辅助函数：获取类型显示值（仅显示预设值或提示信息）
	const getTypeDisplay = (file) => {
	    const type = file.type?.toString();
	    if (!type) return "(未设置类型)";
	    return TECH_DEBT_CONFIG.types.set.has(type) ? type : "(无效类型)";
	};
	
	// 辅助函数：获取优先级显示值（仅显示预设值或提示信息）
	const getPriorityDisplay = (file) => {
	    const priority = file.priority?.toString();
	    if (!priority) return "(未设置优先级)";
	    return TECH_DEBT_CONFIG.priorities.set.has(priority) ? priority : "(无效优先级)";
	};
	
	// 显示结果
	if (invalidFiles.length > 0) {
	    // 输出非预设值文件  
	    const listItems = invalidFiles.map(file => {
	        const displayName = file.aliases || file.file.name;
	        const typeDisplay = getTypeDisplay(file);
	        const priorityDisplay = getPriorityDisplay(file);
	        return `[[${file.file.name}|${displayName}]] - ${typeDisplay} - ${priorityDisplay}`;
	    });
	    
	    // 使用单个Markdown列表输出
	    dv.paragraph(listItems);
	} else {
	    // 如果没有非预设值文件，则按原逻辑处理预设值文件
	    const validFiles = allTechDebtFiles;
	    // 确定目标类型
	    let targetType = null;
	    for (const type of TECH_DEBT_CONFIG.types.presets) {
	        if (validFiles.some(p => p.type === type)) {
	            targetType = type;
	            break;
	        }
	    }
	    if (!targetType) {
	        dv.paragraph("⚠️ 没有待处理的技术债");
	    } else {
	        // 过滤并排序文件
	        const filteredFiles = validFiles
	            .filter(p => p.type === targetType)
	            .sort(p => TECH_DEBT_CONFIG.priorities.order[p.Priority] || 999);
	        
	        if (filteredFiles.length === 0) {
	            dv.paragraph(`⚠️ 没有${targetType}类型的技术债`);
	        } else {          
	            // 创建优先级分组
	            const groupedByPriority = {};
	            filteredFiles.forEach(file => {
	                const priority = file.priority || "未设置";
	                if (!groupedByPriority[priority]) {
	                    groupedByPriority[priority] = [];
	                }
	                groupedByPriority[priority].push(file);
	            });
	            
	            // 收集所有输出行
	            const outputLines = [];
	            TECH_DEBT_CONFIG.priorities.presets.forEach(priority => {
	                if (groupedByPriority[priority]) {
	                    groupedByPriority[priority].forEach(file => {
	                        const displayName = file.aliases || file.file.name;
	                        outputLines.push(
	                            `[[${file.file.name}|${displayName}]] - ${file.type} - ${priority}`
	                        );
	                    });
	                }
	            });
	            // 使用单个Markdown列表输出所有内容
	            dv.paragraph(outputLines);
	        }
	    }
	}
```

# 5. 任务拆解

- [x] 尝试绘制「[[改善工作流程图.svg]]」“路径2”部分内容 ⏳ 2025-08-05 ✅ 2025-08-05
- [x] 探索阻碍优先级排序的可行性方案 ⏳ 2025-08-05 ✅ 2025-08-06
- [x] 编写「[[blockPriority算法说明]]」 ⏳ 2025-08-06 ✅ 2025-08-06
- [x] 为所有历史阻碍添加新字段信息（「阻碍优先级规则计算说明」） ⏳ 2025-08-06 ✅ 2025-08-06
- [x] 编写dataviewjs测试代码（满足「阻碍优先级规则计算说明」） ⏳ 2025-08-06 ✅ 2025-08-06
- [x] 修正阻碍元数据冗余及错误字段（满足「阻碍优先级规则计算说明」） ⏳ 2025-08-07 ✅ 2025-08-07
- [x] 测试人工筛选与代码筛选高优先级阻碍的准确度，并修正异常代码 ⏳ 2025-08-07 ✅ 2025-08-09 ^0cfe34
- [x] 修改「[[TP-Project-Blocker]]」V1.3 ⏳ 2025-08-07 ✅ 2025-08-09
- [x] 修改「[[TP-Project-Retro]]」V1.3 ⏳ 2025-08-07 ✅ 2025-08-09