async function improveSummary({ dv }) {
  const projectName = dv.current().file.path.split("/")[1].trim();
  const targetFolder = `2-项目/${projectName}/4-每周回顾`;
  const tableTitlePattern = "流程改善";

  const currentFileName = dv.current().file.name;
  const weekMatch = currentFileName.match(/^Retro-(\d{4})-WK(\d{2})$/);
  if (!weekMatch) {
    dv.el("p", "⚠️ 模板文件中查询不生效");
    return;
  }

  const targetYear = parseInt(weekMatch[1]);
  const targetWeek = parseInt(weekMatch[2]);
  let allTableData = [];

  const candidateFiles = [];
  for (let file of dv.pages(`"${targetFolder}"`).file) {
    const fileMatch = file.name.match(/^Retro-(\d{4})-WK(\d{2})$/);
    if (!fileMatch) continue;

    const fileYear = parseInt(fileMatch[1]);
    const fileWeek = parseInt(fileMatch[2]);

    if (
      fileYear < targetYear ||
      (fileYear === targetYear && fileWeek < targetWeek)
    ) {
      candidateFiles.push({
        file,
        year: fileYear,
        week: fileWeek,
        path: file.path,
      });
    }
  }
  //保持文件时间顺序
  candidateFiles.sort((a, b) => {
    if (a.year !== b.year) return b.year - a.year;
    return b.week - a.week;
  });

  for (let candidate of candidateFiles) {
    const { file, year, week, path } = candidate;

    const content = await dv.io.load(path);

    const headingRegex = new RegExp(
      `(?:^|\\n)#+\\s*.*${tableTitlePattern}.*[^\\n]*\\n([\\s\\S]*?)(?=\\n#|$)`,
      "i"
    );

    const match = content.match(headingRegex);
    if (!match || !match[1]) continue;

    const sectionContent = match[1].trim();

    const parseMarkdownTables = (markdown) => {
      const tables = [];
      const tableRegex =
        /\|([^\n]+\|)\s*\n\s*\|(\s*:?[-~]+:?\s*\|)+\s*\n((?:\s*\|[^\n]+\|[^\S\r?\n]*\n?)+)/g;

      let tableMatch;
      while ((tableMatch = tableRegex.exec(markdown)) !== null) {
        try {
          const headerRow = tableMatch[1]
            .split("|")
            .map((cell) => cell.trim())
            .filter((cell) => cell !== "");

          const dataRows = tableMatch[3]
            .split("\n")
            .filter(
              (row) =>
                row.trim() !== "" && row.includes("|") && !row.startsWith("|--")
            )
            .map((row) => {
              const cells = row
                .split("|")
                .slice(1, -1)
                .map((cell) => {
                  const normalized = cell
                    .trim()
                    .replace(/<br>/gi, "\n")
                    .replace(/\s*\n\s*/g, "\n")
                    .trim();
                  return normalized;
                });
              return cells;
            })
            .filter((row) => row.some((cell) => cell !== ""));

          if (dataRows.length > 0) {
            tables.push({ header: headerRow, data: dataRows });
          }
        } catch (e) {
          console.warn("表格解析错误:", e);
        }
      }
      return tables;
    };

    const tables = parseMarkdownTables(sectionContent);

    if (tables.length > 0) {
      const weekTag = `${year}-WK${week.toString().padStart(2, "0")}`;
      const fileName = `Retro-${weekTag}.md`;
      const fileLink = dv.fileLink(
        `${targetFolder}/${fileName}`,
        false,
        fileName
      );

      for (const table of tables) {
        const { header, data } = table;

        if (allTableData.length === 0) {
          allTableData.push([...header, "回顾链接"]);
        }

        data.forEach((row) => {
          allTableData.push([...row, fileLink]);
        });
      }
    }
  }

  if (allTableData.length > 0) {
    dv.table(allTableData[0], allTableData.slice(1));
  } else {
    dv.el("p", "🎉 无历史流程改善数据");
  }
}

await improveSummary({ dv });